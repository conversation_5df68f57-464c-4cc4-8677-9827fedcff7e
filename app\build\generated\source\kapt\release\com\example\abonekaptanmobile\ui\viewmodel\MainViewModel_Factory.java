// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.ui.viewmodel;

import android.content.Context;
import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import com.example.abonekaptanmobile.data.repository.EmailRepository;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import com.example.abonekaptanmobile.data.repository.GmailRepository;
import com.example.abonekaptanmobile.data.repository.SubscriptionRepository;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import com.example.abonekaptanmobile.utils.HybridApproachTester;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<Context> contextProvider;

  private final Provider<GoogleAuthManager> googleAuthManagerProvider;

  private final Provider<GmailRepository> gmailRepositoryProvider;

  private final Provider<EmailRepository> emailRepositoryProvider;

  private final Provider<SubscriptionRepository> subscriptionRepositoryProvider;

  private final Provider<SubscriptionClassifier> subscriptionClassifierProvider;

  private final Provider<FeedbackRepository> feedbackRepositoryProvider;

  private final Provider<HybridApproachTester> hybridApproachTesterProvider;

  public MainViewModel_Factory(Provider<Context> contextProvider,
      Provider<GoogleAuthManager> googleAuthManagerProvider,
      Provider<GmailRepository> gmailRepositoryProvider,
      Provider<EmailRepository> emailRepositoryProvider,
      Provider<SubscriptionRepository> subscriptionRepositoryProvider,
      Provider<SubscriptionClassifier> subscriptionClassifierProvider,
      Provider<FeedbackRepository> feedbackRepositoryProvider,
      Provider<HybridApproachTester> hybridApproachTesterProvider) {
    this.contextProvider = contextProvider;
    this.googleAuthManagerProvider = googleAuthManagerProvider;
    this.gmailRepositoryProvider = gmailRepositoryProvider;
    this.emailRepositoryProvider = emailRepositoryProvider;
    this.subscriptionRepositoryProvider = subscriptionRepositoryProvider;
    this.subscriptionClassifierProvider = subscriptionClassifierProvider;
    this.feedbackRepositoryProvider = feedbackRepositoryProvider;
    this.hybridApproachTesterProvider = hybridApproachTesterProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(contextProvider.get(), googleAuthManagerProvider.get(), gmailRepositoryProvider.get(), emailRepositoryProvider.get(), subscriptionRepositoryProvider.get(), subscriptionClassifierProvider.get(), feedbackRepositoryProvider.get(), hybridApproachTesterProvider.get());
  }

  public static MainViewModel_Factory create(Provider<Context> contextProvider,
      Provider<GoogleAuthManager> googleAuthManagerProvider,
      Provider<GmailRepository> gmailRepositoryProvider,
      Provider<EmailRepository> emailRepositoryProvider,
      Provider<SubscriptionRepository> subscriptionRepositoryProvider,
      Provider<SubscriptionClassifier> subscriptionClassifierProvider,
      Provider<FeedbackRepository> feedbackRepositoryProvider,
      Provider<HybridApproachTester> hybridApproachTesterProvider) {
    return new MainViewModel_Factory(contextProvider, googleAuthManagerProvider, gmailRepositoryProvider, emailRepositoryProvider, subscriptionRepositoryProvider, subscriptionClassifierProvider, feedbackRepositoryProvider, hybridApproachTesterProvider);
  }

  public static MainViewModel newInstance(Context context, GoogleAuthManager googleAuthManager,
      GmailRepository gmailRepository, EmailRepository emailRepository,
      SubscriptionRepository subscriptionRepository, SubscriptionClassifier subscriptionClassifier,
      FeedbackRepository feedbackRepository, HybridApproachTester hybridApproachTester) {
    return new MainViewModel(context, googleAuthManager, gmailRepository, emailRepository, subscriptionRepository, subscriptionClassifier, feedbackRepository, hybridApproachTester);
  }
}
