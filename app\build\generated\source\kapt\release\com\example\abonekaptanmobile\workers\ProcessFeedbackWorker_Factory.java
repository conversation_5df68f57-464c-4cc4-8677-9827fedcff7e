// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.workers;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProcessFeedbackWorker_Factory {
  private final Provider<FeedbackRepository> feedbackRepoProvider;

  private final Provider<CommunityPatternRepository> patternRepoProvider;

  private final Provider<AppDatabase> appDatabaseProvider;

  public ProcessFeedbackWorker_Factory(Provider<FeedbackRepository> feedbackRepoProvider,
      Provider<CommunityPatternRepository> patternRepoProvider,
      Provider<AppDatabase> appDatabaseProvider) {
    this.feedbackRepoProvider = feedbackRepoProvider;
    this.patternRepoProvider = patternRepoProvider;
    this.appDatabaseProvider = appDatabaseProvider;
  }

  public ProcessFeedbackWorker get(Context appContext, WorkerParameters workerParams) {
    return newInstance(appContext, workerParams, feedbackRepoProvider.get(), patternRepoProvider.get(), appDatabaseProvider.get());
  }

  public static ProcessFeedbackWorker_Factory create(
      Provider<FeedbackRepository> feedbackRepoProvider,
      Provider<CommunityPatternRepository> patternRepoProvider,
      Provider<AppDatabase> appDatabaseProvider) {
    return new ProcessFeedbackWorker_Factory(feedbackRepoProvider, patternRepoProvider, appDatabaseProvider);
  }

  public static ProcessFeedbackWorker newInstance(Context appContext, WorkerParameters workerParams,
      FeedbackRepository feedbackRepo, CommunityPatternRepository patternRepo,
      AppDatabase appDatabase) {
    return new ProcessFeedbackWorker(appContext, workerParams, feedbackRepo, patternRepo, appDatabase);
  }
}
