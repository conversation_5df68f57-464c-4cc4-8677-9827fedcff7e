// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideOkHttpClientFactory implements Factory<OkHttpClient> {
  private final Provider<GoogleAuthManager> authManagerProvider;

  public AppModule_ProvideOkHttpClientFactory(Provider<GoogleAuthManager> authManagerProvider) {
    this.authManagerProvider = authManagerProvider;
  }

  @Override
  public OkHttpClient get() {
    return provideOkHttpClient(authManagerProvider.get());
  }

  public static AppModule_ProvideOkHttpClientFactory create(
      Provider<GoogleAuthManager> authManagerProvider) {
    return new AppModule_ProvideOkHttpClientFactory(authManagerProvider);
  }

  public static OkHttpClient provideOkHttpClient(GoogleAuthManager authManager) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideOkHttpClient(authManager));
  }
}
