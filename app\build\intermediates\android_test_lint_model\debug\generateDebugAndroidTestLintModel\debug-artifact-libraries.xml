<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.hilt:hilt-work:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b6ef651f14c8c1b4198b3e1b4f667\transformed\hilt-work-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-work:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b6ef651f14c8c1b4198b3e1b4f667\transformed\hilt-work-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0862665a5a0d04caaacd7ad9f122f17\transformed\hilt-navigation-compose-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation-compose:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d0862665a5a0d04caaacd7ad9f122f17\transformed\hilt-navigation-compose-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8f90c468c8ce971b2db927d0c0fa0\transformed\hilt-navigation-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ca8f90c468c8ce971b2db927d0c0fa0\transformed\hilt-navigation-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android:2.49@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6985aff9e5a96a24fdc982f9c2cbad06\transformed\hilt-android-2.49\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.49"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6985aff9e5a96a24fdc982f9c2cbad06\transformed\hilt-android-2.49"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth:21.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth:21.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ab3f8438605bbe9b79dfed51418bd4\transformed\play-services-auth-api-phone-18.0.2\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-api-phone:18.0.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\50ab3f8438605bbe9b79dfed51418bd4\transformed\play-services-auth-api-phone-18.0.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0736e4ddbe12619fb8da75e9ceb1173b\transformed\play-services-auth-base-18.0.10\jars\classes.jar"
      resolved="com.google.android.gms:play-services-auth-base:18.0.10"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0736e4ddbe12619fb8da75e9ceb1173b\transformed\play-services-auth-base-18.0.10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-fido:20.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-fido:20.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\db3a7b6af74cf1429029b1abe1c432ca\transformed\play-services-fido-20.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6968c742713d6dc1de5ff4821fe15f32\transformed\play-services-tasks-18.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9c34032e693f558d3bfaaeb1f82e9ed\transformed\fragment-1.5.7\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9c34032e693f558d3bfaaeb1f82e9ed\transformed\fragment-1.5.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac9d68b92fe2ab1028cb5e8476ae3bd\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5ac9d68b92fe2ab1028cb5e8476ae3bd\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f82e8fd986dc7a55ada9e5aad87ba648\transformed\navigation-compose-2.5.1\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.5.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f82e8fd986dc7a55ada9e5aad87ba648\transformed\navigation-compose-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f60cddd10e3adb78377e455819e0e7db\transformed\navigation-runtime-ktx-2.5.1\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.5.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f60cddd10e3adb78377e455819e0e7db\transformed\navigation-runtime-ktx-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9e2d1cf43cde8a32196cdda01a6068\transformed\navigation-runtime-2.5.1\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.5.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e9e2d1cf43cde8a32196cdda01a6068\transformed\navigation-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03425cb92fd71ca562259853bf49cb47\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\03425cb92fd71ca562259853bf49cb47\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d0104178181e4d53999be75c6c66be\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98d0104178181e4d53999be75c6c66be\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02d433c1203c1af9e1f7f0e1ce24e13\transformed\loader-1.1.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e02d433c1203c1af9e1f7f0e1ce24e13\transformed\loader-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef02b094be29d9392cc742f8f83049fd\transformed\espresso-core-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ef02b094be29d9392cc742f8f83049fd\transformed\espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5559c82788100f61a1c61930f4a7ab05\transformed\core-1.5.0\jars\classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5559c82788100f61a1c61930f4a7ab05\transformed\core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98411e9c9435da7f16fa3304181eecc7\transformed\work-runtime-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\98411e9c9435da7f16fa3304181eecc7\transformed\work-runtime-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adf2c11f76fef535c1060fff21c61aed\transformed\lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\adf2c11f76fef535c1060fff21c61aed\transformed\lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8b76eb325d7efbddd4cb92c49c77079\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d8b76eb325d7efbddd4cb92c49c77079\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a09d6bd8eaa5be3f4376ce9ed7cf1b1\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a09d6bd8eaa5be3f4376ce9ed7cf1b1\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"
      provided="true"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058afe60a62462f8a077b2f93aad827c\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\058afe60a62462f8a077b2f93aad827c\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad2304e64c5a702977fad6696ea04d\transformed\navigation-common-ktx-2.5.1\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.5.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\46ad2304e64c5a702977fad6696ea04d\transformed\navigation-common-ktx-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd203b27384ed6abbecfa6eb1dbc818e\transformed\navigation-common-2.5.1\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.5.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd203b27384ed6abbecfa6eb1dbc818e\transformed\navigation-common-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c174996be18f1bb84fe8d589527704\transformed\lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47c174996be18f1bb84fe8d589527704\transformed\lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0600dfc23bb70309fa0471f8b174b8\transformed\core-ktx-1.12.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f0600dfc23bb70309fa0471f8b174b8\transformed\core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ee1bd6a009846944d541900d2397703\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ee1bd6a009846944d541900d2397703\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f8512599b3d55e93013e5ec39d4c7\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f67f8512599b3d55e93013e5ec39d4c7\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\jars\classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b9074ea43515050688821caed3e085a\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b9074ea43515050688821caed3e085a\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc6090eb890ea60e9895ac2d90c5f54\transformed\lifecycle-runtime-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fdc6090eb890ea60e9895ac2d90c5f54\transformed\lifecycle-runtime-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755b1088b89d0e690888cbb7a34e5b94\transformed\lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\755b1088b89d0e690888cbb7a34e5b94\transformed\lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6df12ac5967402e13f4181ddc3c39571\transformed\lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6df12ac5967402e13f4181ddc3c39571\transformed\lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\553fdf91606a07a84412c88833cf4ae2\transformed\lifecycle-viewmodel-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\553fdf91606a07a84412c88833cf4ae2\transformed\lifecycle-viewmodel-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1062e727b039c203a5f49af9a364889\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e1062e727b039c203a5f49af9a364889\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b71b3088e9666a0519ca03cd7fb2af\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\16b71b3088e9666a0519ca03cd7fb2af\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a47a47129b71dadb07682124089ee59\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a47a47129b71dadb07682124089ee59\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ff4138f34d916ab64efc1ad9ff9bbe4\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ff4138f34d916ab64efc1ad9ff9bbe4\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f6650a0e991f193d9beef7fe5f16423\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6f6650a0e991f193d9beef7fe5f16423\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441006add3d247ad75ec852593ccf6f8\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\441006add3d247ad75ec852593ccf6f8\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7512fb7757540f965633433483f2aea8\transformed\material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7512fb7757540f965633433483f2aea8\transformed\material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93ea021be202f37b2a37351cc45d3653\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93ea021be202f37b2a37351cc45d3653\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc04b051877837630ff27043bddcc536\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fc04b051877837630ff27043bddcc536\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae9863232b9716e1e6ec45d64efe02de\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ae9863232b9716e1e6ec45d64efe02de\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4606d9d9f45968c7915b277b29a9662\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4606d9d9f45968c7915b277b29a9662\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a40bf90101796a14a05da6bf9d6b8fe\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a40bf90101796a14a05da6bf9d6b8fe\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ffdefbe13277925eff2fd0eb5e23821\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ffdefbe13277925eff2fd0eb5e23821\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b007dd394f7caff8a2cf46ca84f545df\transformed\ui-test-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b007dd394f7caff8a2cf46ca84f545df\transformed\ui-test-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da9ca9f265ea25a5c32e964881d59f17\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da9ca9f265ea25a5c32e964881d59f17\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\066a0ad3a2dc6f9cdea2e59ec7063442\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\066a0ad3a2dc6f9cdea2e59ec7063442\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\758d06e28d876accf8cd86f1d399c64f\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\758d06e28d876accf8cd86f1d399c64f\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.6.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-junit4-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7b65cbc74b132f46051a64fa08fd5d9\transformed\ui-test-junit4-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-junit4-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b7b65cbc74b132f46051a64fa08fd5d9\transformed\ui-test-junit4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86c0883850ca31797c15aee283ae1111\transformed\junit-1.1.5\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\86c0883850ca31797c15aee283ae1111\transformed\junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"
      provided="true"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38c461e1a459c353e8424d45ad6935e3\transformed\room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\38c461e1a459c353e8424d45ad6935e3\transformed\room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"
      provided="true"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbf81b3fa31fd696ace58d247b1e5d5\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7bbf81b3fa31fd696ace58d247b1e5d5\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f69793395e8c81844fe849a95e93ba9\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.6.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2f69793395e8c81844fe849a95e93ba9\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-test-jvm\1.7.1\46877b802bede2a4b97ad2abe770685190ad8706\kotlinx-coroutines-test-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.1\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"
      provided="true"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bac779ab956903c5c380030ef3c9c56c\transformed\runner-1.5.2\jars\classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bac779ab956903c5c380030ef3c9c56c\transformed\runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f2a2fe85fd53ee28d3e2e9c356c82f6\transformed\storage-1.4.2\jars\classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f2a2fe85fd53ee28d3e2e9c356c82f6\transformed\storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f40e63528d26c5dd08d55da2da84878\transformed\monitor-1.6.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f40e63528d26c5dd08d55da2da84878\transformed\monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da019e8cfac2bee18432c8f4599e8c57\transformed\annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da019e8cfac2bee18432c8f4599e8c57\transformed\annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5fc50ae51481312102ab347d031230\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a5fc50ae51481312102ab347d031230\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e51bf216bfe5af090a1433c05af959\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\62e51bf216bfe5af090a1433c05af959\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67663ff03f7437bfd40a417d66c88a5b\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67663ff03f7437bfd40a417d66c88a5b\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f47d1133a0d23573d29136b38f5ad5\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f47d1133a0d23573d29136b38f5ad5\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2366fe92f73a3c88d21f4742a2b39a90\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2366fe92f73a3c88d21f4742a2b39a90\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8e6074fcf09995d920a59ab173fea8ae\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a869f15ba42402e929d2b68c92b9e30\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8a869f15ba42402e929d2b68c92b9e30\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.21\17ee3e873d439566c7d8354403b5f3d9744c4c9c\kotlin-stdlib-1.9.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.21"/>
  <library
      name="com.google.api-client:google-api-client-android:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.api-client\google-api-client-android\2.2.0\7dfa82a86397a76734f6f9ee5c56784ae2d4d8f1\google-api-client-android-2.2.0.jar"
      resolved="com.google.api-client:google-api-client-android:2.2.0"
      provided="true"/>
  <library
      name="com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.apis\google-api-services-gmail\v1-rev20220404-2.0.0\66aadab67169b27e504fb5fb8eed55f17c005756\google-api-services-gmail-v1-rev20220404-2.0.0.jar"
      resolved="com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0"
      provided="true"/>
  <library
      name="com.google.auth:google-auth-library-oauth2-http:1.23.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auth\google-auth-library-oauth2-http\1.23.0\f9ebd75a55b8e2cfa62e1f66d04a62b46a2f3b70\google-auth-library-oauth2-http-1.23.0.jar"
      resolved="com.google.auth:google-auth-library-oauth2-http:1.23.0"
      provided="true"/>
  <library
      name="com.google.auth:google-auth-library-credentials:1.23.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auth\google-auth-library-credentials\1.23.0\a50ee3611922a0eea9d421c6ddb1db031972a7dc\google-auth-library-credentials-1.23.0.jar"
      resolved="com.google.auth:google-auth-library-credentials:1.23.0"
      provided="true"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.api-client:google-api-client:2.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.api-client\google-api-client\2.0.0\c3daf5048a26a73182b5e6c81ad141fb859f0787\google-api-client-2.0.0.jar"
      resolved="com.google.api-client:google-api-client:2.0.0"
      provided="true"/>
  <library
      name="com.google.oauth-client:google-oauth-client:1.34.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.oauth-client\google-oauth-client\1.34.1\4a4f88c5e13143f882268c98239fb85c3b2c6cb2\google-oauth-client-1.34.1.jar"
      resolved="com.google.oauth-client:google-oauth-client:1.34.1"
      provided="true"/>
  <library
      name="com.google.http-client:google-http-client-gson:1.43.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client-gson\1.43.3\252e267acf720ef6333488740a696a1d5e204639\google-http-client-gson-1.43.3.jar"
      resolved="com.google.http-client:google-http-client-gson:1.43.3"
      provided="true"/>
  <library
      name="com.google.http-client:google-http-client-apache-v2:1.42.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client-apache-v2\1.42.1\949cc0ed789e76c1ec539d8a5e60ee527b22b6c4\google-http-client-apache-v2-1.42.1.jar"
      resolved="com.google.http-client:google-http-client-apache-v2:1.42.1"
      provided="true"/>
  <library
      name="com.google.http-client:google-http-client:1.43.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.http-client\google-http-client\1.43.3\a758b82e55a2f5f681e289c5ed384d3dbda6f3cd\google-http-client-1.43.3.jar"
      resolved="com.google.http-client:google-http-client:1.43.3"
      provided="true"/>
  <library
      name="io.opencensus:opencensus-contrib-http-util:0.31.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.opencensus\opencensus-contrib-http-util\0.31.1\3c13fc5715231fadb16a9b74a44d9d59c460cfa8\opencensus-contrib-http-util-0.31.1.jar"
      resolved="io.opencensus:opencensus-contrib-http-util:0.31.1"
      provided="true"/>
  <library
      name="com.google.guava:guava:32.0.0-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.0.0-android\33e32530743192664d79543a4f40576930d23d45\guava-32.0.0-android.jar"
      resolved="com.google.guava:guava:32.0.0-android"
      provided="true"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2bc77b08baa5e058442f01d3b8740064\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d88d238a2bf956356ce0d4cd266b5363\transformed\tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d88d238a2bf956356ce0d4cd266b5363\transformed\tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-core:2.49@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.49\dec1221f1b48d8e46f38bcc1953dac3b74a4641f\hilt-core-2.49.jar"
      resolved="com.google.dagger:hilt-core:2.49"
      provided="true"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\870732a7f7b1a3f7a00131aa2c6927c1\transformed\espresso-idling-resource-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\870732a7f7b1a3f7a00131aa2c6927c1\transformed\espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="com.google.dagger:dagger:2.49@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.49\97fd74aa05761e8ea9bb5d02245f39eae30b5483\dagger-2.49.jar"
      resolved="com.google.dagger:dagger:2.49"
      provided="true"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"
      provided="true"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.10.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.10.4\9679de8286eb0a151db6538ba297a8951c4a1224\auto-value-annotations-1.10.4.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.10.4"
      provided="true"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.18.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.18.0\89b684257096f548fa39a7df9fdaa409d4d4df91\error_prone_annotations-2.18.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.18.0"
      provided="true"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.49@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93876383fd2f0373b1a38e7e8869f4f8\transformed\dagger-lint-aar-2.49\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.49"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\93876383fd2f0373b1a38e7e8869f4f8\transformed\dagger-lint-aar-2.49"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-common:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.hilt\hilt-common\1.1.0\9ca9c006cfce81d1435a0735fdab4b9e1166faa1\hilt-common-1.1.0.jar"
      resolved="androidx.hilt:hilt-common:1.1.0"
      provided="true"/>
  <library
      name="org.apache.httpcomponents:httpclient:4.5.14@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpclient\4.5.14\1194890e6f56ec29177673f2f12d0b8e627dec98\httpclient-4.5.14.jar"
      resolved="org.apache.httpcomponents:httpclient:4.5.14"
      provided="true"/>
  <library
      name="org.apache.httpcomponents:httpcore:4.4.16@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpcore\4.4.16\51cf043c87253c9f58b539c9f7e44c8894223850\httpcore-4.4.16.jar"
      resolved="org.apache.httpcomponents:httpcore:4.4.16"
      provided="true"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="io.opencensus:opencensus-api:0.31.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.opencensus\opencensus-api\0.31.1\66a60c7201c2b8b20ce495f0295b32bb0ccbbc57\opencensus-api-0.31.1.jar"
      resolved="io.opencensus:opencensus-api:0.31.1"
      provided="true"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"
      provided="true"/>
  <library
      name="org.checkerframework:checker-qual:3.33.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.33.0\de2b60b62da487644fc11f734e73c8b0b431238f\checker-qual-3.33.0.jar"
      resolved="org.checkerframework:checker-qual:3.33.0"
      provided="true"/>
  <library
      name="commons-logging:commons-logging:1.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-logging\commons-logging\1.2\4bfc12adfe4842bf07b657f0369c4cb522955686\commons-logging-1.2.jar"
      resolved="commons-logging:commons-logging:1.2"
      provided="true"/>
  <library
      name="commons-codec:commons-codec:1.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.11\3acb4705652e16236558f0f4f2192cc33c3bd189\commons-codec-1.11.jar"
      resolved="commons-codec:commons-codec:1.11"
      provided="true"/>
  <library
      name="io.grpc:grpc-context:1.27.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.grpc\grpc-context\1.27.2\1789190601b7a5361e4fa52b6bc95ec2cd71e854\grpc-context-1.27.2.jar"
      resolved="io.grpc:grpc-context:1.27.2"
      provided="true"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\736535d916a2f202cc5de9983b95bb18\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\736535d916a2f202cc5de9983b95bb18\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8da8d98afa6f240c0f54189b1a77c0\transformed\autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5d8da8d98afa6f240c0f54189b1a77c0\transformed\autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c7557b32a33e18709389bce01f0583\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c7557b32a33e18709389bce01f0583\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
