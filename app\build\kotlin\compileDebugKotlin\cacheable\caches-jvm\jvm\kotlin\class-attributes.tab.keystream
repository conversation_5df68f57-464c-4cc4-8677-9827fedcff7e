,com.example.abonekaptanmobile.AboneKaptanApp*com.example.abonekaptanmobile.MainActivity4com.example.abonekaptanmobile.auth.GoogleAuthManager4com.example.abonekaptanmobile.data.local.AppDatabase><EMAIL><com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;com.example.abonekaptanmobile.data.local.entity.EmailEntity>com.example.abonekaptanmobile.data.local.entity.FeedbackEntityBcom.example.abonekaptanmobile.data.local.entity.SubscriptionEntityIcom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;com.example.abonekaptanmobile.data.local.entity.PatternType8com.example.abonekaptanmobile.data.remote.HuggingFaceApi2com.example.abonekaptanmobile.data.remote.GmailApiHcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse9com.example.abonekaptanmobile.data.remote.model.MessageId<com.example.abonekaptanmobile.data.remote.model.GmailMessage>com.example.abonekaptanmobile.data.remote.model.MessagePayload=com.example.abonekaptanmobile.data.remote.model.MessageHeader?com.example.abonekaptanmobile.data.remote.model.MessagePartBodyBcom.example.abonekaptanmobile.data.remote.model.HuggingFaceRequestEcom.example.abonekaptanmobile.data.remote.model.HuggingFaceParametersCcom.example.abonekaptanmobile.data.remote.model.HuggingFaceResponseDcom.example.abonekaptanmobile.data.remote.model.ClassificationResultNcom.example.abonekaptanmobile.data.remote.model.ClassificationResult.CompanionLcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResultFcom.example.abonekaptanmobile.data.remote.model.HybridValidationResult<com.example.abonekaptanmobile.data.remote.model.LlamaRequest>com.example.abonekaptanmobile.data.remote.model.ReplicateInput?com.example.abonekaptanmobile.data.remote.model.LlamaParameters=com.example.abonekaptanmobile.data.remote.model.LlamaResponseGcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.CompanionPcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequestScom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParametersQcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse[com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse.CompanionFcom.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResultGcom.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatusHcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository=<EMAIL>=com.example.abonekaptanmobile.data.repository.GmailRepositoryCcom.example.abonekaptanmobile.data.repository.HuggingFaceRepositoryMcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.CompanionDcom.example.abonekaptanmobile.data.repository.SubscriptionRepository*com.example.abonekaptanmobile.di.AppModule4com.example.abonekaptanmobile.model.CancellationInfo3com.example.abonekaptanmobile.model.ClassifiedEmail4com.example.abonekaptanmobile.model.SubscriptionType-com.example.abonekaptanmobile.model.EmailType,com.example.abonekaptanmobile.model.RawEmail4com.example.abonekaptanmobile.model.SubscriptionItem6com.example.abonekaptanmobile.model.SubscriptionStatus=com.example.abonekaptanmobile.services.SubscriptionClassifier8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel.com.example.abonekaptanmobile.utils.TestResult8com.example.abonekaptanmobile.utils.HybridApproachTesterBcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion;com.example.abonekaptanmobile.workers.ProcessFeedbackWorkerEcom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion                                                                                                                                                                                           