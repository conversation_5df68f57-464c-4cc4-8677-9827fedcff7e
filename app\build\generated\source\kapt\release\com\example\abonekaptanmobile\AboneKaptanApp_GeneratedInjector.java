package com.example.abonekaptanmobile;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = AboneKaptanApp.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface AboneKaptanApp_GeneratedInjector {
  void injectAboneKaptanApp(AboneKaptanApp aboneKaptanApp);
}
