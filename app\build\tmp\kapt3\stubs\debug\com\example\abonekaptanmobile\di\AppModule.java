package com.example.abonekaptanmobile.di;

import android.content.Context;
import android.util.Log;
import androidx.room.Room;
import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.remote.GmailApi;
import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import com.example.abonekaptanmobile.data.repository.GmailRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import com.example.abonekaptanmobile.data.repository.EmailRepository;
import com.example.abonekaptanmobile.data.repository.SubscriptionRepository;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import dagger.Module;
import dagger.Provides;
import dagger.hilt.InstallIn;
import dagger.hilt.android.qualifiers.ApplicationContext;
import dagger.hilt.components.SingletonComponent;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.inject.Singleton;

@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000|\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\bH\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u000eH\u0007J\u0010\u0010\u0011\u001a\u00020\u00122\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010\u0013\u001a\u00020\u00142\u0006\u0010\f\u001a\u00020\u0012H\u0007J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0007J\u0010\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u0016H\u0007J\u0012\u0010\u001c\u001a\u00020\u001d2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u001e\u001a\u00020\u001fH\u0007J\u0010\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u001fH\u0007J\u0010\u0010#\u001a\u00020\u00182\u0006\u0010$\u001a\u00020\u001dH\u0007J\u0018\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u000b2\u0006\u0010(\u001a\u00020!H\u0007J\u0010\u0010)\u001a\u00020*2\u0006\u0010\t\u001a\u00020\u0004H\u0007J\u0010\u0010+\u001a\u00020,2\u0006\u0010\f\u001a\u00020*H\u0007\u00a8\u0006-"}, d2 = {"Lcom/example/abonekaptanmobile/di/AppModule;", "", "()V", "provideAppDatabase", "Lcom/example/abonekaptanmobile/data/local/AppDatabase;", "context", "Landroid/content/Context;", "provideCommunityPatternDao", "Lcom/example/abonekaptanmobile/data/local/dao/CommunityPatternDao;", "appDatabase", "provideCommunityPatternRepository", "Lcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository;", "dao", "provideEmailDao", "Lcom/example/abonekaptanmobile/data/local/dao/EmailDao;", "provideEmailRepository", "Lcom/example/abonekaptanmobile/data/repository/EmailRepository;", "provideFeedbackDao", "Lcom/example/abonekaptanmobile/data/local/dao/FeedbackDao;", "provideFeedbackRepository", "Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;", "provideGmailApi", "Lcom/example/abonekaptanmobile/data/remote/GmailApi;", "okHttpClient", "Lokhttp3/OkHttpClient;", "provideGmailRepository", "Lcom/example/abonekaptanmobile/data/repository/GmailRepository;", "gmailApi", "provideGoogleAuthManager", "Lcom/example/abonekaptanmobile/auth/GoogleAuthManager;", "provideHuggingFaceApi", "Lcom/example/abonekaptanmobile/data/remote/HuggingFaceApi;", "provideHuggingFaceRepository", "Lcom/example/abonekaptanmobile/data/repository/HuggingFaceRepository;", "huggingFaceApi", "provideOkHttpClient", "authManager", "provideSubscriptionClassifier", "Lcom/example/abonekaptanmobile/services/SubscriptionClassifier;", "communityPatternRepo", "huggingFaceRepository", "provideSubscriptionDao", "Lcom/example/abonekaptanmobile/data/local/dao/SubscriptionDao;", "provideSubscriptionRepository", "Lcom/example/abonekaptanmobile/data/repository/SubscriptionRepository;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class AppModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.di.AppModule INSTANCE = null;
    
    private AppModule() {
        super();
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.auth.GoogleAuthManager provideGoogleAuthManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient provideOkHttpClient(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.auth.GoogleAuthManager authManager) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.GmailApi provideGmailApi(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.GmailRepository provideGmailRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.GmailApi gmailApi) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.local.AppDatabase provideAppDatabase(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao provideCommunityPatternDao(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.AppDatabase appDatabase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.local.dao.FeedbackDao provideFeedbackDao(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.AppDatabase appDatabase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.local.dao.EmailDao provideEmailDao(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.AppDatabase appDatabase) {
        return null;
    }
    
    @javax.inject.Singleton()
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.local.dao.SubscriptionDao provideSubscriptionDao(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.AppDatabase appDatabase) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.CommunityPatternRepository provideCommunityPatternRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao dao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.FeedbackRepository provideFeedbackRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.FeedbackDao dao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.EmailRepository provideEmailRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.EmailDao dao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.SubscriptionRepository provideSubscriptionRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.SubscriptionDao dao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.HuggingFaceApi provideHuggingFaceApi() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.repository.HuggingFaceRepository provideHuggingFaceRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.HuggingFaceApi huggingFaceApi) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.services.SubscriptionClassifier provideSubscriptionClassifier(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.CommunityPatternRepository communityPatternRepo, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.HuggingFaceRepository huggingFaceRepository) {
        return null;
    }
}