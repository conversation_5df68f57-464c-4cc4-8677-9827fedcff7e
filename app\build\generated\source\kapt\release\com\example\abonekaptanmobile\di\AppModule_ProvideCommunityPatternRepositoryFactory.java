// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideCommunityPatternRepositoryFactory implements Factory<CommunityPatternRepository> {
  private final Provider<CommunityPatternDao> daoProvider;

  public AppModule_ProvideCommunityPatternRepositoryFactory(
      Provider<CommunityPatternDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public CommunityPatternRepository get() {
    return provideCommunityPatternRepository(daoProvider.get());
  }

  public static AppModule_ProvideCommunityPatternRepositoryFactory create(
      Provider<CommunityPatternDao> daoProvider) {
    return new AppModule_ProvideCommunityPatternRepositoryFactory(daoProvider);
  }

  public static CommunityPatternRepository provideCommunityPatternRepository(
      CommunityPatternDao dao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCommunityPatternRepository(dao));
  }
}
