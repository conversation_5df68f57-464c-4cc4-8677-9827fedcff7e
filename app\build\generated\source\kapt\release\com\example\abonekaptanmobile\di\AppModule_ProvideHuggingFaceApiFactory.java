// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideHuggingFaceApiFactory implements Factory<HuggingFaceApi> {
  @Override
  public HuggingFaceApi get() {
    return provideHuggingFaceApi();
  }

  public static AppModule_ProvideHuggingFaceApiFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HuggingFaceApi provideHuggingFaceApi() {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideHuggingFaceApi());
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideHuggingFaceApiFactory INSTANCE = new AppModule_ProvideHuggingFaceApiFactory();
  }
}
