package com.example.abonekaptanmobile.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Turkish: Abonelik veritabanı entity'si.
 * English: Subscription database entity.
 */
@Entity(tableName = "subscriptions")
data class SubscriptionEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val serviceName: String,
    val status: String, // ACTIVE, CANCELLED, UNKNOWN
    val emailCount: Int,
    val lastEmailDate: Long,
    val cancellationDate: Long? = null,
    val subscriptionStartDate: Long? = null,
    val relatedEmailIds: String // Comma-separated list of email IDs
)
