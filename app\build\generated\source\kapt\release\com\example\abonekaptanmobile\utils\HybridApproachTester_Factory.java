// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.utils;

import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HybridApproachTester_Factory implements Factory<HybridApproachTester> {
  private final Provider<HuggingFaceRepository> huggingFaceRepositoryProvider;

  public HybridApproachTester_Factory(
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    this.huggingFaceRepositoryProvider = huggingFaceRepositoryProvider;
  }

  @Override
  public HybridApproachTester get() {
    return newInstance(huggingFaceRepositoryProvider.get());
  }

  public static HybridApproachTester_Factory create(
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    return new HybridApproachTester_Factory(huggingFaceRepositoryProvider);
  }

  public static HybridApproachTester newInstance(HuggingFaceRepository huggingFaceRepository) {
    return new HybridApproachTester(huggingFaceRepository);
  }
}
