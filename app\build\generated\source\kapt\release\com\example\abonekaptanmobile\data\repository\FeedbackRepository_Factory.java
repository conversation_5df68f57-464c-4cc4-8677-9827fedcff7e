// Generated by Da<PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FeedbackRepository_Factory implements Factory<FeedbackRepository> {
  private final Provider<FeedbackDao> daoProvider;

  public FeedbackRepository_Factory(Provider<FeedbackDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public FeedbackRepository get() {
    return newInstance(daoProvider.get());
  }

  public static FeedbackRepository_Factory create(Provider<FeedbackDao> daoProvider) {
    return new FeedbackRepository_Factory(daoProvider);
  }

  public static FeedbackRepository newInstance(FeedbackDao dao) {
    return new FeedbackRepository(dao);
  }
}
