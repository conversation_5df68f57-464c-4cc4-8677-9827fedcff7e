// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HuggingFaceRepository_Factory implements Factory<HuggingFaceRepository> {
  private final Provider<HuggingFaceApi> huggingFaceApiProvider;

  public HuggingFaceRepository_Factory(Provider<HuggingFaceApi> huggingFaceApiProvider) {
    this.huggingFaceApiProvider = huggingFaceApiProvider;
  }

  @Override
  public HuggingFaceRepository get() {
    return newInstance(huggingFaceApiProvider.get());
  }

  public static HuggingFaceRepository_Factory create(
      Provider<HuggingFaceApi> huggingFaceApiProvider) {
    return new HuggingFaceRepository_Factory(huggingFaceApiProvider);
  }

  public static HuggingFaceRepository newInstance(HuggingFaceApi huggingFaceApi) {
    return new HuggingFaceRepository(huggingFaceApi);
  }
}
