package com.example.abonekaptanmobile.data.repository

import android.util.Log
import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao
import com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics
import com.example.abonekaptanmobile.data.local.dao.CompanyStatistics
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult
import com.example.abonekaptanmobile.data.remote.model.RawEmail
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Turkish: İki aşamalı analiz sonuçları için repository.
 * English: Repository for two-stage analysis results.
 */
@Singleton
class StageAnalysisRepository @Inject constructor(
    private val stageAnalysisDao: StageAnalysisDao
) {
    companion object {
        private const val TAG = "StageAnalysisRepository"
    }

    /**
     * Turkish: Tüm analiz sonuçlarını getir.
     * English: Get all analysis results.
     */
    fun getAllAnalysisResults(): Flow<List<StageAnalysisEntity>> {
        return stageAnalysisDao.getAllAnalysisResults()
    }

    /**
     * Turkish: Aşama 1'i tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 1.
     */
    fun getStage1CompletedEmails(minConfidence: Float = 0.6f): Flow<List<StageAnalysisEntity>> {
        return stageAnalysisDao.getStage1CompletedEmails(minConfidence)
    }

    /**
     * Turkish: Aşama 2'ye geçecek e-postaları getir.
     * English: Get emails ready for stage 2.
     */
    fun getEmailsForStage2(minConfidence: Float = 0.6f): Flow<List<StageAnalysisEntity>> {
        return stageAnalysisDao.getEmailsForStage2(minConfidence)
    }

    /**
     * Turkish: Aşama 2'yi tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 2.
     */
    fun getStage2CompletedEmails(): Flow<List<StageAnalysisEntity>> {
        return stageAnalysisDao.getStage2CompletedEmails()
    }

    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    fun getAnalysisResultsByCompany(companyName: String): Flow<List<StageAnalysisEntity>> {
        return stageAnalysisDao.getAnalysisResultsByCompany(companyName)
    }

    /**
     * Turkish: Aşama 1 sonucunu kaydet.
     * English: Save stage 1 result.
     */
    suspend fun saveStage1Result(
        result: TwoStageAnalysisResult,
        email: RawEmail
    ): Long {
        try {
            val entity = StageAnalysisEntity(
                emailIndex = result.emailIndex,
                emailId = email.id,
                domain = result.domain,
                subject = result.subject,
                from = email.from,
                date = email.date,
                stage1_isSubscriptionCompany = result.isSubscriptionCompany,
                stage1_companyName = result.companyName,
                stage1_companyConfidence = result.companyConfidence,
                stage1_timestamp = System.currentTimeMillis(),
                processingStatus = if (result.isSubscriptionCompany && result.companyConfidence >= 0.6f) 
                    "STAGE1_COMPLETED" else "STAGE1_FAILED"
            )
            
            val id = stageAnalysisDao.insertAnalysisResult(entity)
            Log.d(TAG, "Aşama 1 sonucu kaydedildi: ID=$id, Şirket=${result.companyName}, Güven=${result.companyConfidence}")
            return id
        } catch (e: Exception) {
            Log.e(TAG, "Aşama 1 sonucu kaydetme hatası: ${e.message}", e)
            throw e
        }
    }

    /**
     * Turkish: Aşama 2 sonucunu güncelle.
     * English: Update stage 2 result.
     */
    suspend fun updateStage2Result(
        entityId: Long,
        emailType: String,
        confidence: Float,
        rawContent: String
    ) {
        try {
            stageAnalysisDao.updateStage2Results(
                id = entityId,
                emailType = emailType,
                confidence = confidence,
                timestamp = System.currentTimeMillis(),
                rawContent = rawContent
            )
            Log.d(TAG, "Aşama 2 sonucu güncellendi: ID=$entityId, Tür=$emailType, Güven=$confidence")
        } catch (e: Exception) {
            Log.e(TAG, "Aşama 2 sonucu güncelleme hatası: ${e.message}", e)
            throw e
        }
    }

    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    suspend fun updateProcessingStatus(entityId: Long, status: String) {
        try {
            stageAnalysisDao.updateProcessingStatus(entityId, status)
            Log.d(TAG, "İşlem durumu güncellendi: ID=$entityId, Durum=$status")
        } catch (e: Exception) {
            Log.e(TAG, "İşlem durumu güncelleme hatası: ${e.message}", e)
            throw e
        }
    }

    /**
     * Turkish: E-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    suspend fun getAnalysisResultByEmailId(emailId: String): StageAnalysisEntity? {
        return try {
            stageAnalysisDao.getAnalysisResultByEmailId(emailId)
        } catch (e: Exception) {
            Log.e(TAG, "E-posta ID'sine göre analiz sonucu getirme hatası: ${e.message}", e)
            null
        }
    }

    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    suspend fun deleteAllAnalysisResults() {
        try {
            stageAnalysisDao.deleteAllAnalysisResults()
            Log.i(TAG, "Tüm analiz sonuçları silindi")
        } catch (e: Exception) {
            Log.e(TAG, "Analiz sonuçları silme hatası: ${e.message}", e)
            throw e
        }
    }

    /**
     * Turkish: Eski analiz sonuçlarını sil.
     * English: Delete old analysis results.
     */
    suspend fun deleteOldAnalysisResults(daysOld: Int = 30) {
        try {
            val cutoffTime = System.currentTimeMillis() - (daysOld * 24 * 60 * 60 * 1000L)
            stageAnalysisDao.deleteOldAnalysisResults(cutoffTime)
            Log.i(TAG, "$daysOld günden eski analiz sonuçları silindi")
        } catch (e: Exception) {
            Log.e(TAG, "Eski analiz sonuçları silme hatası: ${e.message}", e)
            throw e
        }
    }

    /**
     * Turkish: Analiz istatistiklerini getir.
     * English: Get analysis statistics.
     */
    suspend fun getAnalysisStatistics(): AnalysisStatistics? {
        return try {
            stageAnalysisDao.getAnalysisStatistics()
        } catch (e: Exception) {
            Log.e(TAG, "Analiz istatistikleri getirme hatası: ${e.message}", e)
            null
        }
    }

    /**
     * Turkish: Şirket bazında istatistikleri getir.
     * English: Get company-based statistics.
     */
    suspend fun getCompanyStatistics(): List<CompanyStatistics> {
        return try {
            stageAnalysisDao.getCompanyStatistics()
        } catch (e: Exception) {
            Log.e(TAG, "Şirket istatistikleri getirme hatası: ${e.message}", e)
            emptyList()
        }
    }

    /**
     * Turkish: Birden fazla aşama 1 sonucunu toplu kaydet.
     * English: Bulk save multiple stage 1 results.
     */
    suspend fun saveStage1Results(
        results: List<Pair<TwoStageAnalysisResult, RawEmail>>
    ) {
        try {
            val entities = results.map { (result, email) ->
                StageAnalysisEntity(
                    emailIndex = result.emailIndex,
                    emailId = email.id,
                    domain = result.domain,
                    subject = result.subject,
                    from = email.from,
                    date = email.date,
                    stage1_isSubscriptionCompany = result.isSubscriptionCompany,
                    stage1_companyName = result.companyName,
                    stage1_companyConfidence = result.companyConfidence,
                    stage1_timestamp = System.currentTimeMillis(),
                    processingStatus = if (result.isSubscriptionCompany && result.companyConfidence >= 0.6f) 
                        "STAGE1_COMPLETED" else "STAGE1_FAILED"
                )
            }
            
            stageAnalysisDao.insertAnalysisResults(entities)
            Log.i(TAG, "${entities.size} adet aşama 1 sonucu toplu olarak kaydedildi")
        } catch (e: Exception) {
            Log.e(TAG, "Toplu aşama 1 sonucu kaydetme hatası: ${e.message}", e)
            throw e
        }
    }
}
