// file: app/java/com/example/abonekaptanmobile/data/repository/HuggingFaceRepository.kt
package com.example.abonekaptanmobile.data.repository

import android.util.Log
import com.example.abonekaptanmobile.data.remote.HuggingFaceApi
import com.example.abonekaptanmobile.data.remote.model.ClassificationResult
import com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceParameters
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest
import com.example.abonekaptanmobile.data.remote.model.HybridValidationResult
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse
import com.example.abonekaptanmobile.data.remote.model.LlamaRequest
import com.example.abonekaptanmobile.data.remote.model.LlamaParameters
import com.example.abonekaptanmobile.data.remote.model.LlamaResponse
import com.example.abonekaptanmobile.data.remote.model.ReplicateInput
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult
import com.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Turkish: Hugging Face API ile etkileşim için repository sınıfı.
 * English: Repository class for interacting with Hugging Face API.
 */
@Singleton
class HuggingFaceRepository @Inject constructor(
    private val huggingFaceApi: HuggingFaceApi
) {
    companion object {
        private const val AUTH_TOKEN = "Bearer *************************************"
        private const val TAG = "HuggingFaceRepository"

        // Hybrid Approach: 100 Şirket Listesi (Domain-Şirket Eşleştirme için)
        private val SUBSCRIPTION_COMPANIES = listOf(
            "Netflix", "Amazon Prime Video", "Disney+", "HBO Max", "Hulu", "Peacock", "Paramount+",
            "Crunchyroll", "Starz", "ESPN+", "DAZN", "MUBI", "JioCinema", "Globoplay", "Viu",
            "Discovery+", "Zee5", "RTL+", "ivi TV", "Shahid", "YouTube Premium", "Spotify",
            "Apple Music", "Amazon Music", "Tidal", "Deezer", "SoundCloud Go", "Audible",
            "Kindle Unlimited", "Storytel", "Medium", "Scribd", "DIGITURK TOD", "Exxen", "BluTV",
            "Gain", "D-Smart GO", "Tv+", "Coursera", "Udemy", "LinkedIn Learning", "MasterClass",
            "Khan Academy", "Codecademy", "Udacity", "Skillshare", "Duolingo Plus", "Babbel",
            "Rosetta Stone", "Brilliant", "Photomath", "Pluralsight", "Peloton", "ClassPass",
            "Apple Fitness+", "Fitbit Premium", "Calm", "Headspace", "WW", "Noom", "Tonal",
            "Mirror", "Zwift", "BetterHelp", "Talkspace", "Nike Training Club", "Strava Premium",
            "Bloomberg Terminal", "The Wall Street Journal", "Financial Times", "The Economist",
            "The New York Times", "The Washington Post", "Forbes", "Barron's", "Yahoo Finance Premium",
            "Seeking Alpha", "Morningstar Premium", "Reuters News", "Xbox Game Pass", "PlayStation Plus",
            "Nintendo Switch Online", "EA Play", "Apple Arcade", "Ubisoft+", "Twitch", "Discord Nitro",
            "Roblox Premium", "PlayStation Now", "Microsoft 365", "Google Workspace", "Adobe Creative Cloud",
            "Salesforce", "AWS", "Microsoft Azure", "Zoom", "Slack", "Atlassian Jira", "Atlassian Confluence",
            "Dropbox", "Box", "GitHub", "GitLab", "Canva", "Asana", "Trello", "DocuSign", "Eventbrite",
            "Grammarly", "Webflow", "HubSpot", "Mailchimp", "SurveyMonkey", "Zendesk", "ServiceNow",
            "Twilio", "Autodesk", "Shopify", "Amazon Prime", "Costco", "Sam's Club", "HelloFresh",
            "Blue Apron", "Dollar Shave Club", "Birchbox", "BarkBox", "Graze", "Patreon", "OnlyFans",
            "other"
        )

        // Hybrid Approach: Email Türü Sınıflandırması (Şirket tespit edildikten sonra)
        private val EMAIL_TYPE_LABELS = listOf(
            "subscription_start",           // Abonelik başlatma
            "subscription_cancel",          // Abonelik iptal
            "subscription_renewal",         // Abonelik yenileme
            "payment_confirmation",         // Ödeme onayı
            "welcome_message",             // Hoş geldin mesajı
            "promotional_offer",           // Promosyon teklifi
            "billing_notification",        // Fatura bildirimi
            "account_notification",        // Hesap bildirimi
            "service_update",              // Hizmet güncellemesi
            "other"                        // Diğer
        )

        // Abonelik sınıflandırması için etiketler (eski sistem - backward compatibility)
        private val SUBSCRIPTION_LABELS = listOf("paid_subscription", "free_subscription", "not_subscription", "promotional")

        // Abonelik durumu sınıflandırması için etiketler (eski sistem - backward compatibility)
        private val SUBSCRIPTION_STATUS_LABELS = listOf(
            "subscription_start",
            "subscription_cancel",
            "subscription_renewal",
            "payment_confirmation",
            "welcome_message",
            "promotional_message",
            "other"
        )
    }

    /**
     * Turkish: Verilen metni abonelik olup olmadığına göre sınıflandırır.
     * English: Classifies the given text as subscription or not.
     */
    suspend fun classifySubscription(emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying subscription for email content: ${emailContent.take(100)}...")

            val request = HuggingFaceRequest(
                inputs = emailContent,
                parameters = HuggingFaceParameters(candidateLabels = SUBSCRIPTION_LABELS)
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Classification results: $results")

            // En yüksek skora sahip sonucu döndür
            return@withContext results.maxByOrNull { it.score } ?: ClassificationResult("unknown", 0f)
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying subscription: ${e.message}", e)
            return@withContext ClassificationResult("error", 0f)
        }
    }

    /**
     * Turkish: Verilen metni abonelik durumuna göre sınıflandırır (başlangıç, iptal, yenileme).
     * English: Classifies the given text based on subscription status (start, cancel, renewal).
     */
    suspend fun classifySubscriptionStatus(emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying subscription status for email content: ${emailContent.take(100)}...")

            val request = HuggingFaceRequest(
                inputs = emailContent,
                parameters = HuggingFaceParameters(candidateLabels = SUBSCRIPTION_STATUS_LABELS)
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Subscription status results: $results")

            // En yüksek skora sahip sonucu döndür
            return@withContext results.maxByOrNull { it.score } ?: ClassificationResult("unknown", 0f)
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying subscription status: ${e.message}", e)
            return@withContext ClassificationResult("error", 0f)
        }
    }

    /**
     * Turkish: Verilen metni mail türüne göre detaylı olarak sınıflandırır.
     * English: Classifies the given text based on email type in detail.
     */
    suspend fun classifyEmailType(emailContent: String): DetailedClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying email type for content: ${emailContent.take(100)}...")

            val request = HuggingFaceRequest(
                inputs = emailContent,
                parameters = HuggingFaceParameters(candidateLabels = EMAIL_TYPE_LABELS)
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Email type classification results: $results")

            // Tüm sonuçları döndür
            val topResults = results.sortedByDescending { it.score }.take(3)

            // En yüksek skora sahip sonucu ve tüm sonuçları döndür
            val primaryResult = topResults.firstOrNull() ?: ClassificationResult("unknown", 0f)
            return@withContext DetailedClassificationResult(
                primaryLabel = primaryResult.label,
                primaryScore = primaryResult.score,
                allResults = topResults
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying email type: ${e.message}", e)
            return@withContext DetailedClassificationResult(
                primaryLabel = "error",
                primaryScore = 0f,
                allResults = listOf(ClassificationResult("error", 0f))
            )
        }
    }

    /**
     * Turkish: Verilen metni ücretli abonelik olup olmadığına göre sınıflandırır.
     * English: Classifies the given text as paid subscription or not.
     */
    suspend fun classifyPaidSubscription(emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Classifying paid subscription for email content: ${emailContent.take(100)}...")

            val request = HuggingFaceRequest(
                inputs = emailContent,
                parameters = HuggingFaceParameters(candidateLabels = listOf("paid_subscription", "free_or_not_subscription"))
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Paid subscription classification results: $results")

            // En yüksek skora sahip sonucu döndür
            return@withContext results.maxByOrNull { it.score } ?: ClassificationResult("unknown", 0f)
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying paid subscription: ${e.message}", e)
            return@withContext ClassificationResult("error", 0f)
        }
    }

    // ==================== HYBRID APPROACH FUNCTIONS ====================

    /**
     * Turkish: Hybrid Approach - Adım 1: Email domain'ini analiz ederek şirket tespiti yapar.
     * English: Hybrid Approach - Step 1: Analyzes email domain to identify the company.
     */
    suspend fun classifyCompanyFromDomain(emailDomain: String, emailContent: String): ClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Hybrid Step 1: Classifying company from domain: $emailDomain")

            // Domain ve email içeriğini birleştirerek daha iyi sonuç al
            val combinedInput = "Email domain: $emailDomain\nEmail content: ${emailContent.take(200)}"

            val request = HuggingFaceRequest(
                inputs = combinedInput,
                parameters = HuggingFaceParameters(candidateLabels = SUBSCRIPTION_COMPANIES)
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Company classification results: ${results.take(5)}")

            // En yüksek skora sahip sonucu döndür
            val bestResult = results.maxByOrNull { it.score } ?: ClassificationResult("other", 0f)

            Log.d(TAG, "Hybrid Step 1 Result: Company = ${bestResult.label}, Confidence = ${bestResult.score}")
            return@withContext bestResult
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying company from domain: ${e.message}", e)
            return@withContext ClassificationResult("other", 0f)
        }
    }

    /**
     * Turkish: Hybrid Approach - Adım 2: Tespit edilen şirket için email türünü belirler.
     * English: Hybrid Approach - Step 2: Determines email type for the identified company.
     */
    suspend fun classifyEmailTypeForCompany(emailContent: String, companyName: String): DetailedClassificationResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Hybrid Step 2: Classifying email type for company: $companyName")

            // Şirket adını da dahil ederek daha spesifik sınıflandırma yap
            val enhancedInput = "Company: $companyName\nEmail content: $emailContent"

            val request = HuggingFaceRequest(
                inputs = enhancedInput,
                parameters = HuggingFaceParameters(candidateLabels = EMAIL_TYPE_LABELS)
            )

            val response = huggingFaceApi.classifyText(AUTH_TOKEN, request)
            val results = ClassificationResult.fromResponse(response)

            Log.d(TAG, "Email type classification results for $companyName: $results")

            // Tüm sonuçları döndür
            val topResults = results.sortedByDescending { it.score }.take(3)
            val primaryResult = topResults.firstOrNull() ?: ClassificationResult("other", 0f)

            val detailedResult = DetailedClassificationResult(
                primaryLabel = primaryResult.label,
                primaryScore = primaryResult.score,
                allResults = topResults
            )

            Log.d(TAG, "Hybrid Step 2 Result: Email Type = ${primaryResult.label}, Confidence = ${primaryResult.score}")
            return@withContext detailedResult
        } catch (e: Exception) {
            Log.e(TAG, "Error classifying email type for company: ${e.message}", e)
            return@withContext DetailedClassificationResult(
                primaryLabel = "other",
                primaryScore = 0f,
                allResults = listOf(ClassificationResult("other", 0f))
            )
        }
    }

    /**
     * Turkish: Hybrid Approach - Adım 3: Confidence score'ları analiz ederek final doğrulama yapar.
     * English: Hybrid Approach - Step 3: Analyzes confidence scores for final validation.
     */
    fun validateHybridResults(
        companyResult: ClassificationResult,
        emailTypeResult: DetailedClassificationResult,
        minCompanyConfidence: Float = 0.6f,
        minEmailTypeConfidence: Float = 0.5f,
        minOverallConfidence: Float = 0.7f
    ): HybridValidationResult {

        val companyConfidence = companyResult.score
        val emailTypeConfidence = emailTypeResult.primaryScore
        val overallConfidence = (companyConfidence + emailTypeConfidence) / 2

        val isCompanyReliable = companyConfidence >= minCompanyConfidence && companyResult.label != "other"
        val isEmailTypeReliable = emailTypeConfidence >= minEmailTypeConfidence && emailTypeResult.primaryLabel != "other"
        val isOverallReliable = overallConfidence >= minOverallConfidence

        val finalReliability = isCompanyReliable && isEmailTypeReliable && isOverallReliable

        Log.d(TAG, "Hybrid Step 3 Validation:")
        Log.d(TAG, "  Company: ${companyResult.label} (${companyConfidence}) - Reliable: $isCompanyReliable")
        Log.d(TAG, "  Email Type: ${emailTypeResult.primaryLabel} (${emailTypeConfidence}) - Reliable: $isEmailTypeReliable")
        Log.d(TAG, "  Overall Confidence: $overallConfidence - Reliable: $isOverallReliable")
        Log.d(TAG, "  Final Result: ${if (finalReliability) "RELIABLE" else "NOT RELIABLE"}")

        return HybridValidationResult(
            companyName = companyResult.label,
            emailType = emailTypeResult.primaryLabel,
            companyConfidence = companyConfidence,
            emailTypeConfidence = emailTypeConfidence,
            overallConfidence = overallConfidence,
            isReliable = finalReliability,
            detailedEmailTypeResult = emailTypeResult
        )
    }

    // ==================== YENİ İKİ AŞAMALI SİSTEM ====================



    /**
     * Turkish: Aşama 1 - E-posta başlık ve domain'ini analiz ederek ücretli abonelik şirketi tespiti.
     * English: Stage 1 - Analyze email subject and domain to detect paid subscription companies.
     */
    suspend fun analyzeEmailForSubscriptionCompany(
        emailIndex: Int,
        domain: String,
        subject: String
    ): TwoStageAnalysisResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Aşama 1: E-posta $emailIndex analiz ediliyor - Domain: $domain, Subject: ${subject.take(50)}")

            val prompt = """
E-posta bilgileri:
Domain: $domain
Başlık: $subject

Bu e-posta aşağıdaki ücretli abonelik şirketlerinden birine ait mi?
${SUBSCRIPTION_COMPANIES.joinToString(", ")}

Sadece şu formatla cevap ver:
Şirket: [şirket adı veya "bilinmeyen"]
Güven: [0.0-1.0 arası sayı]
Abonelik: [evet/hayır]

Cevap:
""".trimIndent()

            val request = HuggingFaceTextGenerationRequest(
                inputs = prompt,
                parameters = HuggingFaceTextGenerationParameters(
                    maxNewTokens = 100,
                    temperature = 0.1f,
                    doSample = false
                )
            )

            val response = huggingFaceApi.generateText(AUTH_TOKEN, request)
            val generatedText = response.firstOrNull()?.get("generated_text") ?: ""
            val result = parseLlamaCompanyResponse(generatedText)

            Log.d(TAG, "Aşama 1 Sonuç: Şirket=${result.companyName}, Güven=${result.companyConfidence}, Abonelik=${result.isSubscriptionCompany}")

            return@withContext TwoStageAnalysisResult(
                emailIndex = emailIndex,
                domain = domain,
                subject = subject,
                isSubscriptionCompany = result.isSubscriptionCompany,
                companyName = result.companyName,
                companyConfidence = result.companyConfidence
            )

        } catch (e: Exception) {
            Log.e(TAG, "Aşama 1 hatası: ${e.message}", e)
            return@withContext TwoStageAnalysisResult(
                emailIndex = emailIndex,
                domain = domain,
                subject = subject,
                isSubscriptionCompany = false,
                companyName = "bilinmeyen",
                companyConfidence = 0.0f
            )
        }
    }

    /**
     * Turkish: Aşama 2 - Tespit edilen abonelik e-postalarını türlerine göre sınıflandır.
     * English: Stage 2 - Classify detected subscription emails by their types.
     */
    suspend fun classifySubscriptionEmailType(
        emailContent: String,
        companyName: String
    ): Pair<String, Float> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Aşama 2: $companyName şirketi için e-posta türü analizi")

            val prompt = """
E-posta metni:
---
$emailContent
---

Bu e-posta $companyName şirketi ile ilgili ne türde bir e-posta?

Sadece şu üç kategoriden birini seç:
1. abonelik başlangıcı - Yeni abonelik başlatma, hoş geldin, ilk ödeme onayı
2. abonelik iptali - Abonelik iptal, hesap kapatma, son ödeme
3. alakasız - Promosyon, pazarlama, genel bilgilendirme

Sadece kategori adını yaz:
""".trimIndent()

            val request = HuggingFaceTextGenerationRequest(
                inputs = prompt,
                parameters = HuggingFaceTextGenerationParameters(
                    maxNewTokens = 50,
                    temperature = 0.1f,
                    doSample = false
                )
            )

            val response = huggingFaceApi.generateText(AUTH_TOKEN, request)
            val generatedText = response.firstOrNull()?.get("generated_text") ?: ""
            val result = parseLlamaEmailTypeResponse(generatedText)

            Log.d(TAG, "Aşama 2 Sonuç: Tür=${result.first}, Güven=${result.second}")

            return@withContext result

        } catch (e: Exception) {
            Log.e(TAG, "Aşama 2 hatası: ${e.message}", e)
            return@withContext Pair("alakasız", 0.0f)
        }
    }

    /**
     * Turkish: Llama modelinden gelen şirket analizi cevabını parse eder.
     * English: Parses company analysis response from Llama model.
     */
    private fun parseLlamaCompanyResponse(response: String): TwoStageAnalysisResult {
        try {
            val lines = response.trim().split("\n")
            var companyName = "bilinmeyen"
            var confidence = 0.0f
            var isSubscription = false

            for (line in lines) {
                when {
                    line.startsWith("Şirket:", ignoreCase = true) -> {
                        companyName = line.substringAfter(":").trim().lowercase()
                        if (companyName == "bilinmeyen" || companyName == "unknown") {
                            companyName = "bilinmeyen"
                        }
                    }
                    line.startsWith("Güven:", ignoreCase = true) -> {
                        val confidenceStr = line.substringAfter(":").trim()
                        confidence = confidenceStr.toFloatOrNull() ?: 0.0f
                    }
                    line.startsWith("Abonelik:", ignoreCase = true) -> {
                        val subscriptionStr = line.substringAfter(":").trim().lowercase()
                        isSubscription = subscriptionStr == "evet" || subscriptionStr == "yes"
                    }
                }
            }

            // Güven skoru çok düşükse abonelik değil olarak işaretle
            if (confidence < 0.6f) {
                isSubscription = false
                companyName = "bilinmeyen"
            }

            return TwoStageAnalysisResult(
                emailIndex = 0,
                domain = "",
                subject = "",
                isSubscriptionCompany = isSubscription,
                companyName = companyName,
                companyConfidence = confidence
            )

        } catch (e: Exception) {
            Log.e(TAG, "Llama company response parse hatası: ${e.message}")
            return TwoStageAnalysisResult(
                emailIndex = 0,
                domain = "",
                subject = "",
                isSubscriptionCompany = false,
                companyName = "bilinmeyen",
                companyConfidence = 0.0f
            )
        }
    }

    /**
     * Turkish: Llama modelinden gelen e-posta türü cevabını parse eder.
     * English: Parses email type response from Llama model.
     */
    private fun parseLlamaEmailTypeResponse(response: String): Pair<String, Float> {
        try {
            val cleanResponse = response.trim().lowercase()

            return when {
                cleanResponse.contains("abonelik başlangıcı") ||
                cleanResponse.contains("subscription start") ||
                cleanResponse.contains("başlangıç") -> Pair("subscription_start", 0.9f)

                cleanResponse.contains("abonelik iptali") ||
                cleanResponse.contains("subscription cancel") ||
                cleanResponse.contains("iptal") -> Pair("subscription_cancel", 0.9f)

                cleanResponse.contains("alakasız") ||
                cleanResponse.contains("irrelevant") ||
                cleanResponse.contains("other") -> Pair("none", 0.9f)

                else -> Pair("none", 0.5f)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Llama email type response parse hatası: ${e.message}")
            return Pair("none", 0.0f)
        }
    }
}
