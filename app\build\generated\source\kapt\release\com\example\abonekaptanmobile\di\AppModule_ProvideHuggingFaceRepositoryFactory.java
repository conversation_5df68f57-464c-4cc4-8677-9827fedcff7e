// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideHuggingFaceRepositoryFactory implements Factory<HuggingFaceRepository> {
  private final Provider<HuggingFaceApi> huggingFaceApiProvider;

  public AppModule_ProvideHuggingFaceRepositoryFactory(
      Provider<HuggingFaceApi> huggingFaceApiProvider) {
    this.huggingFaceApiProvider = huggingFaceApiProvider;
  }

  @Override
  public HuggingFaceRepository get() {
    return provideHuggingFaceRepository(huggingFaceApiProvider.get());
  }

  public static AppModule_ProvideHuggingFaceRepositoryFactory create(
      Provider<HuggingFaceApi> huggingFaceApiProvider) {
    return new AppModule_ProvideHuggingFaceRepositoryFactory(huggingFaceApiProvider);
  }

  public static HuggingFaceRepository provideHuggingFaceRepository(HuggingFaceApi huggingFaceApi) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideHuggingFaceRepository(huggingFaceApi));
  }
}
