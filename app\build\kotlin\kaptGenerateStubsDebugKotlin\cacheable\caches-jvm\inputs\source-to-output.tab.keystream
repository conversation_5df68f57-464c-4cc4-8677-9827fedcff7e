@app/src/main/java/com/example/abonekaptanmobile/ui/theme/Type.kt^app/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity.ktIapp/src/main/java/com/example/abonekaptanmobile/model/SubscriptionItem.ktVapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailApi.ktHapp/src/main/java/com/example/abonekaptanmobile/model/ClassifiedEmail.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/HuggingFaceApi.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Color.ktLapp/src/main/java/com/example/abonekaptanmobile/ui/screens/FeedbackDialog.ktSapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/FeedbackEntity.kt]app/src/main/java/com/example/abonekaptanmobile/data/repository/CommunityPatternRepository.ktQapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/SubscriptionDao.ktWapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionEntity.ktJapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/EmailDao.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/EmailRepository.ktAapp/src/main/java/com/example/abonekaptanmobile/AboneKaptanApp.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Theme.ktMapp/src/main/java/com/example/abonekaptanmobile/ui/viewmodel/MainViewModel.ktPapp/src/main/java/com/example/abonekaptanmobile/workers/ProcessFeedbackWorker.ktRapp/src/main/java/com/example/abonekaptanmobile/services/SubscriptionClassifier.ktJapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SignInScreen.ktXapp/src/main/java/com/example/abonekaptanmobile/data/repository/HuggingFaceRepository.ktAapp/src/main/java/com/example/abonekaptanmobile/model/RawEmail.ktIapp/src/main/java/com/example/abonekaptanmobile/model/CancellationInfo.ktMapp/src/main/java/com/example/abonekaptanmobile/utils/HybridApproachTester.ktUapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/CommunityPatternDao.ktYapp/src/main/java/com/example/abonekaptanmobile/data/repository/SubscriptionRepository.ktTapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SubscriptionListScreen.ktKapp/src/main/java/com/example/abonekaptanmobile/ui/screens/LabTestDialog.ktIapp/src/main/java/com/example/abonekaptanmobile/auth/GoogleAuthManager.kt?app/src/main/java/com/example/abonekaptanmobile/di/AppModule.ktUapp/src/main/java/com/example/abonekaptanmobile/data/repository/FeedbackRepository.ktQapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailMessage.ktPapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/EmailEntity.ktIapp/src/main/java/com/example/abonekaptanmobile/data/local/AppDatabase.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/GmailRepository.kt?app/src/main/java/com/example/abonekaptanmobile/MainActivity.ktMapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/FeedbackDao.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              