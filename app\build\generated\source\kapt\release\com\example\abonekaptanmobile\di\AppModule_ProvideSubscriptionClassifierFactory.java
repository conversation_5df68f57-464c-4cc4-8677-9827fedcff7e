// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideSubscriptionClassifierFactory implements Factory<SubscriptionClassifier> {
  private final Provider<CommunityPatternRepository> communityPatternRepoProvider;

  private final Provider<HuggingFaceRepository> huggingFaceRepositoryProvider;

  public AppModule_ProvideSubscriptionClassifierFactory(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    this.communityPatternRepoProvider = communityPatternRepoProvider;
    this.huggingFaceRepositoryProvider = huggingFaceRepositoryProvider;
  }

  @Override
  public SubscriptionClassifier get() {
    return provideSubscriptionClassifier(communityPatternRepoProvider.get(), huggingFaceRepositoryProvider.get());
  }

  public static AppModule_ProvideSubscriptionClassifierFactory create(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider) {
    return new AppModule_ProvideSubscriptionClassifierFactory(communityPatternRepoProvider, huggingFaceRepositoryProvider);
  }

  public static SubscriptionClassifier provideSubscriptionClassifier(
      CommunityPatternRepository communityPatternRepo,
      HuggingFaceRepository huggingFaceRepository) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideSubscriptionClassifier(communityPatternRepo, huggingFaceRepository));
  }
}
