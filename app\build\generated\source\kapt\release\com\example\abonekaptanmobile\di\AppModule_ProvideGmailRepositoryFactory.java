// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.GmailApi;
import com.example.abonekaptanmobile.data.repository.GmailRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideGmailRepositoryFactory implements Factory<GmailRepository> {
  private final Provider<GmailApi> gmailApiProvider;

  public AppModule_ProvideGmailRepositoryFactory(Provider<GmailApi> gmailApiProvider) {
    this.gmailApiProvider = gmailApiProvider;
  }

  @Override
  public GmailRepository get() {
    return provideGmailRepository(gmailApiProvider.get());
  }

  public static AppModule_ProvideGmailRepositoryFactory create(
      Provider<GmailApi> gmailApiProvider) {
    return new AppModule_ProvideGmailRepositoryFactory(gmailApiProvider);
  }

  public static GmailRepository provideGmailRepository(GmailApi gmailApi) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideGmailRepository(gmailApi));
  }
}
