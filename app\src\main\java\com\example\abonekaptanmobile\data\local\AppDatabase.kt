// file: app/java/com/example/abonekaptanmobile/data/local/AppDatabase.kt
package com.example.abonekaptanmobile.data.local

import android.util.Log // Loglama için eklendi (Migration içinde kullanılabilir)
import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao
import com.example.abonekaptanmobile.data.local.dao.EmailDao
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity
import com.example.abonekaptanmobile.data.local.entity.EmailEntity
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity
import com.example.abonekaptanmobile.data.local.entity.PatternType // PatternType importu

@Database(
    entities = [SubscriptionPatternEntity::class, FeedbackEntity::class, EmailEntity::class, SubscriptionEntity::class],
    version = 4, // <<--- VERSİYONU 4 YAPIN (yeni tablolar eklendi)
    exportSchema = true
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun communityPatternDao(): CommunityPatternDao
    abstract fun feedbackDao(): FeedbackDao
    abstract fun emailDao(): EmailDao
    abstract fun subscriptionDao(): SubscriptionDao

    companion object {
        const val DATABASE_NAME = "abone_kaptan_db"

        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // SubscriptionPatternEntity'ye isSubscription sütununu ekle (varsayılan true)
                db.execSQL("ALTER TABLE subscription_patterns ADD COLUMN isSubscription INTEGER NOT NULL DEFAULT 1")
                try {
                    db.execSQL("ALTER TABLE feedback ADD COLUMN processed INTEGER NOT NULL DEFAULT 0")
                } catch (e: Exception) {
                    Log.w("Migration_1_2", "Could not add 'processed' column to feedback, it might already exist: ${e.message}")
                }
            }
        }

        // YENİ MIGRATION (Versiyon 2'den 3'e)
        val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // SubscriptionPatternEntity'ye yeni sütunları ekle
                // Varsayılan değerler önemlidir.
                // SQLite'ta boolean için INTEGER kullanılır (0 = false, 1 = true)
                db.execSQL("ALTER TABLE subscription_patterns ADD COLUMN isTrustedSenderDomain INTEGER NOT NULL DEFAULT 0")
                db.execSQL("ALTER TABLE subscription_patterns ADD COLUMN patternType TEXT NOT NULL DEFAULT '${PatternType.UNKNOWN}'")
                db.execSQL("ALTER TABLE subscription_patterns ADD COLUMN priority INTEGER NOT NULL DEFAULT 0")
            }
        }

        // YENİ MIGRATION (Versiyon 3'den 4'e) - E-posta ve Abonelik tabloları
        val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(db: SupportSQLiteDatabase) {
                // E-posta tablosu oluştur
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS emails (
                        id TEXT NOT NULL PRIMARY KEY,
                        `from` TEXT NOT NULL,
                        subject TEXT NOT NULL,
                        snippet TEXT NOT NULL,
                        bodyPlainText TEXT,
                        bodySnippet TEXT,
                        date INTEGER NOT NULL,
                        threadId TEXT NOT NULL,
                        labelIds TEXT NOT NULL
                    )
                """.trimIndent())

                // Abonelik tablosu oluştur
                db.execSQL("""
                    CREATE TABLE IF NOT EXISTS subscriptions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        serviceName TEXT NOT NULL,
                        status TEXT NOT NULL,
                        emailCount INTEGER NOT NULL,
                        lastEmailDate INTEGER NOT NULL,
                        cancellationDate INTEGER,
                        subscriptionStartDate INTEGER,
                        relatedEmailIds TEXT NOT NULL
                    )
                """.trimIndent())
            }
        }
    }
}