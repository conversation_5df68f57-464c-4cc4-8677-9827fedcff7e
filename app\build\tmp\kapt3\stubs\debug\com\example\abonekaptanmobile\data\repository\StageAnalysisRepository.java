package com.example.abonekaptanmobile.data.repository;

import android.util.Log;
import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics;
import com.example.abonekaptanmobile.data.local.dao.CompanyStatistics;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult;
import com.example.abonekaptanmobile.data.remote.model.RawEmail;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * Turkish: İki aşamalı analiz sonuçları için repository.
 * English: Repository for two-stage analysis results.
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u0000 32\u00020\u0001:\u00013B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0007J\u0018\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0012\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\rJ\u0018\u0010\u0010\u001a\u0004\u0018\u00010\u000f2\u0006\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\u0013J\u001a\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\u0006\u0010\u0015\u001a\u00020\u0012J\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0086@\u00a2\u0006\u0002\u0010\u0007J\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u000eH\u0086@\u00a2\u0006\u0002\u0010\u0007J\u001c\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\b\b\u0002\u0010\u001b\u001a\u00020\u001cJ\u001c\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\b\b\u0002\u0010\u001b\u001a\u00020\u001cJ\u0012\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\rJ\u001e\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020$H\u0086@\u00a2\u0006\u0002\u0010%J(\u0010&\u001a\u00020\u00062\u0018\u0010\'\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020$0(0\u000eH\u0086@\u00a2\u0006\u0002\u0010)J\u001e\u0010*\u001a\u00020\u00062\u0006\u0010+\u001a\u00020 2\u0006\u0010,\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010-J.\u0010.\u001a\u00020\u00062\u0006\u0010+\u001a\u00020 2\u0006\u0010/\u001a\u00020\u00122\u0006\u00100\u001a\u00020\u001c2\u0006\u00101\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u00102R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/StageAnalysisRepository;", "", "stageAnalysisDao", "Lcom/example/abonekaptanmobile/data/local/dao/StageAnalysisDao;", "(Lcom/example/abonekaptanmobile/data/local/dao/StageAnalysisDao;)V", "deleteAllAnalysisResults", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldAnalysisResults", "daysOld", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAnalysisResults", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/abonekaptanmobile/data/local/entity/StageAnalysisEntity;", "getAnalysisResultByEmailId", "emailId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAnalysisResultsByCompany", "companyName", "getAnalysisStatistics", "Lcom/example/abonekaptanmobile/data/local/dao/AnalysisStatistics;", "getCompanyStatistics", "Lcom/example/abonekaptanmobile/data/local/dao/CompanyStatistics;", "getEmailsForStage2", "minConfidence", "", "getStage1CompletedEmails", "getStage2CompletedEmails", "saveStage1Result", "", "result", "Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;", "email", "error/NonExistentClass", "(Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;Lerror/NonExistentClass;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveStage1Results", "results", "Lkotlin/Pair;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProcessingStatus", "entityId", "status", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateStage2Result", "emailType", "confidence", "rawContent", "(JLjava/lang/String;FLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class StageAnalysisRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao stageAnalysisDao = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "StageAnalysisRepository";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.repository.StageAnalysisRepository.Companion Companion = null;
    
    @javax.inject.Inject()
    public StageAnalysisRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao stageAnalysisDao) {
        super();
    }
    
    /**
     * Turkish: Tüm analiz sonuçlarını getir.
     * English: Get all analysis results.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAllAnalysisResults() {
        return null;
    }
    
    /**
     * Turkish: Aşama 1'i tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 1.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage1CompletedEmails(float minConfidence) {
        return null;
    }
    
    /**
     * Turkish: Aşama 2'ye geçecek e-postaları getir.
     * English: Get emails ready for stage 2.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getEmailsForStage2(float minConfidence) {
        return null;
    }
    
    /**
     * Turkish: Aşama 2'yi tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 2.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage2CompletedEmails() {
        return null;
    }
    
    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAnalysisResultsByCompany(@org.jetbrains.annotations.NotNull()
    java.lang.String companyName) {
        return null;
    }
    
    /**
     * Turkish: Aşama 1 sonucunu kaydet.
     * English: Save stage 1 result.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveStage1Result(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult result, @org.jetbrains.annotations.NotNull()
    RawEmail email, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    /**
     * Turkish: Aşama 2 sonucunu güncelle.
     * English: Update stage 2 result.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateStage2Result(long entityId, @org.jetbrains.annotations.NotNull()
    java.lang.String emailType, float confidence, @org.jetbrains.annotations.NotNull()
    java.lang.String rawContent, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateProcessingStatus(long entityId, @org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: E-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAnalysisResultByEmailId(@org.jetbrains.annotations.NotNull()
    java.lang.String emailId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> $completion) {
        return null;
    }
    
    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAllAnalysisResults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: Eski analiz sonuçlarını sil.
     * English: Delete old analysis results.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteOldAnalysisResults(int daysOld, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: Analiz istatistiklerini getir.
     * English: Get analysis statistics.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAnalysisStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics> $completion) {
        return null;
    }
    
    /**
     * Turkish: Şirket bazında istatistikleri getir.
     * English: Get company-based statistics.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCompanyStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.dao.CompanyStatistics>> $completion) {
        return null;
    }
    
    /**
     * Turkish: Birden fazla aşama 1 sonucunu toplu kaydet.
     * English: Bulk save multiple stage 1 results.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveStage1Results(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends kotlin.Pair<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult, ? extends RawEmail>> results, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/StageAnalysisRepository$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}