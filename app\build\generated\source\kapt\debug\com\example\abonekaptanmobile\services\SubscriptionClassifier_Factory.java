// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.services;

import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SubscriptionClassifier_Factory implements Factory<SubscriptionClassifier> {
  private final Provider<CommunityPatternRepository> communityPatternRepoProvider;

  private final Provider<HuggingFaceRepository> huggingFaceRepositoryProvider;

  private final Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider;

  public SubscriptionClassifier_Factory(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    this.communityPatternRepoProvider = communityPatternRepoProvider;
    this.huggingFaceRepositoryProvider = huggingFaceRepositoryProvider;
    this.stageAnalysisRepositoryProvider = stageAnalysisRepositoryProvider;
  }

  @Override
  public SubscriptionClassifier get() {
    return newInstance(communityPatternRepoProvider.get(), huggingFaceRepositoryProvider.get(), stageAnalysisRepositoryProvider.get());
  }

  public static SubscriptionClassifier_Factory create(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<HuggingFaceRepository> huggingFaceRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    return new SubscriptionClassifier_Factory(communityPatternRepoProvider, huggingFaceRepositoryProvider, stageAnalysisRepositoryProvider);
  }

  public static SubscriptionClassifier newInstance(CommunityPatternRepository communityPatternRepo,
      HuggingFaceRepository huggingFaceRepository,
      StageAnalysisRepository stageAnalysisRepository) {
    return new SubscriptionClassifier(communityPatternRepo, huggingFaceRepository, stageAnalysisRepository);
  }
}
