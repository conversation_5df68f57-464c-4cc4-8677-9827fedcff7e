package com.example.abonekaptanmobile.data.local;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao_Impl;
import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.local.dao.EmailDao_Impl;
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao_Impl;
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile CommunityPatternDao _communityPatternDao;

  private volatile FeedbackDao _feedbackDao;

  private volatile EmailDao _emailDao;

  private volatile SubscriptionDao _subscriptionDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(4) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `subscription_patterns` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `serviceName` TEXT NOT NULL, `regexPattern` TEXT NOT NULL, `source` TEXT NOT NULL, `approvedCount` INTEGER NOT NULL, `rejectedCount` INTEGER NOT NULL, `isSubscription` INTEGER NOT NULL, `isTrustedSenderDomain` INTEGER NOT NULL, `patternType` TEXT NOT NULL, `priority` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_subscription_patterns_serviceName` ON `subscription_patterns` (`serviceName`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_subscription_patterns_regexPattern` ON `subscription_patterns` (`regexPattern`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `feedback` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `serviceName` TEXT NOT NULL, `originalStatus` TEXT NOT NULL, `feedbackLabel` TEXT NOT NULL, `feedbackNote` TEXT, `createdAt` INTEGER NOT NULL, `processed` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `emails` (`id` TEXT NOT NULL, `from` TEXT NOT NULL, `subject` TEXT NOT NULL, `snippet` TEXT NOT NULL, `bodyPlainText` TEXT, `bodySnippet` TEXT, `date` INTEGER NOT NULL, `threadId` TEXT NOT NULL, `labelIds` TEXT NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `subscriptions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `serviceName` TEXT NOT NULL, `status` TEXT NOT NULL, `emailCount` INTEGER NOT NULL, `lastEmailDate` INTEGER NOT NULL, `cancellationDate` INTEGER, `subscriptionStartDate` INTEGER, `relatedEmailIds` TEXT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'fea6219401237f10edfa2519b8fecb19')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `subscription_patterns`");
        db.execSQL("DROP TABLE IF EXISTS `feedback`");
        db.execSQL("DROP TABLE IF EXISTS `emails`");
        db.execSQL("DROP TABLE IF EXISTS `subscriptions`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsSubscriptionPatterns = new HashMap<String, TableInfo.Column>(12);
        _columnsSubscriptionPatterns.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("serviceName", new TableInfo.Column("serviceName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("regexPattern", new TableInfo.Column("regexPattern", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("source", new TableInfo.Column("source", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("approvedCount", new TableInfo.Column("approvedCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("rejectedCount", new TableInfo.Column("rejectedCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("isSubscription", new TableInfo.Column("isSubscription", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("isTrustedSenderDomain", new TableInfo.Column("isTrustedSenderDomain", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("patternType", new TableInfo.Column("patternType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("priority", new TableInfo.Column("priority", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptionPatterns.put("updatedAt", new TableInfo.Column("updatedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSubscriptionPatterns = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSubscriptionPatterns = new HashSet<TableInfo.Index>(2);
        _indicesSubscriptionPatterns.add(new TableInfo.Index("index_subscription_patterns_serviceName", false, Arrays.asList("serviceName"), Arrays.asList("ASC")));
        _indicesSubscriptionPatterns.add(new TableInfo.Index("index_subscription_patterns_regexPattern", false, Arrays.asList("regexPattern"), Arrays.asList("ASC")));
        final TableInfo _infoSubscriptionPatterns = new TableInfo("subscription_patterns", _columnsSubscriptionPatterns, _foreignKeysSubscriptionPatterns, _indicesSubscriptionPatterns);
        final TableInfo _existingSubscriptionPatterns = TableInfo.read(db, "subscription_patterns");
        if (!_infoSubscriptionPatterns.equals(_existingSubscriptionPatterns)) {
          return new RoomOpenHelper.ValidationResult(false, "subscription_patterns(com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity).\n"
                  + " Expected:\n" + _infoSubscriptionPatterns + "\n"
                  + " Found:\n" + _existingSubscriptionPatterns);
        }
        final HashMap<String, TableInfo.Column> _columnsFeedback = new HashMap<String, TableInfo.Column>(7);
        _columnsFeedback.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("serviceName", new TableInfo.Column("serviceName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("originalStatus", new TableInfo.Column("originalStatus", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("feedbackLabel", new TableInfo.Column("feedbackLabel", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("feedbackNote", new TableInfo.Column("feedbackNote", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFeedback.put("processed", new TableInfo.Column("processed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysFeedback = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesFeedback = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoFeedback = new TableInfo("feedback", _columnsFeedback, _foreignKeysFeedback, _indicesFeedback);
        final TableInfo _existingFeedback = TableInfo.read(db, "feedback");
        if (!_infoFeedback.equals(_existingFeedback)) {
          return new RoomOpenHelper.ValidationResult(false, "feedback(com.example.abonekaptanmobile.data.local.entity.FeedbackEntity).\n"
                  + " Expected:\n" + _infoFeedback + "\n"
                  + " Found:\n" + _existingFeedback);
        }
        final HashMap<String, TableInfo.Column> _columnsEmails = new HashMap<String, TableInfo.Column>(9);
        _columnsEmails.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("from", new TableInfo.Column("from", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("subject", new TableInfo.Column("subject", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("snippet", new TableInfo.Column("snippet", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("bodyPlainText", new TableInfo.Column("bodyPlainText", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("bodySnippet", new TableInfo.Column("bodySnippet", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("date", new TableInfo.Column("date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("threadId", new TableInfo.Column("threadId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsEmails.put("labelIds", new TableInfo.Column("labelIds", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysEmails = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesEmails = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoEmails = new TableInfo("emails", _columnsEmails, _foreignKeysEmails, _indicesEmails);
        final TableInfo _existingEmails = TableInfo.read(db, "emails");
        if (!_infoEmails.equals(_existingEmails)) {
          return new RoomOpenHelper.ValidationResult(false, "emails(com.example.abonekaptanmobile.data.local.entity.EmailEntity).\n"
                  + " Expected:\n" + _infoEmails + "\n"
                  + " Found:\n" + _existingEmails);
        }
        final HashMap<String, TableInfo.Column> _columnsSubscriptions = new HashMap<String, TableInfo.Column>(8);
        _columnsSubscriptions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("serviceName", new TableInfo.Column("serviceName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("emailCount", new TableInfo.Column("emailCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("lastEmailDate", new TableInfo.Column("lastEmailDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("cancellationDate", new TableInfo.Column("cancellationDate", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("subscriptionStartDate", new TableInfo.Column("subscriptionStartDate", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSubscriptions.put("relatedEmailIds", new TableInfo.Column("relatedEmailIds", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSubscriptions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSubscriptions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSubscriptions = new TableInfo("subscriptions", _columnsSubscriptions, _foreignKeysSubscriptions, _indicesSubscriptions);
        final TableInfo _existingSubscriptions = TableInfo.read(db, "subscriptions");
        if (!_infoSubscriptions.equals(_existingSubscriptions)) {
          return new RoomOpenHelper.ValidationResult(false, "subscriptions(com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity).\n"
                  + " Expected:\n" + _infoSubscriptions + "\n"
                  + " Found:\n" + _existingSubscriptions);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "fea6219401237f10edfa2519b8fecb19", "23356fc39c6948574a7160abf347b0d3");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "subscription_patterns","feedback","emails","subscriptions");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `subscription_patterns`");
      _db.execSQL("DELETE FROM `feedback`");
      _db.execSQL("DELETE FROM `emails`");
      _db.execSQL("DELETE FROM `subscriptions`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(CommunityPatternDao.class, CommunityPatternDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(FeedbackDao.class, FeedbackDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(EmailDao.class, EmailDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(SubscriptionDao.class, SubscriptionDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public CommunityPatternDao communityPatternDao() {
    if (_communityPatternDao != null) {
      return _communityPatternDao;
    } else {
      synchronized(this) {
        if(_communityPatternDao == null) {
          _communityPatternDao = new CommunityPatternDao_Impl(this);
        }
        return _communityPatternDao;
      }
    }
  }

  @Override
  public FeedbackDao feedbackDao() {
    if (_feedbackDao != null) {
      return _feedbackDao;
    } else {
      synchronized(this) {
        if(_feedbackDao == null) {
          _feedbackDao = new FeedbackDao_Impl(this);
        }
        return _feedbackDao;
      }
    }
  }

  @Override
  public EmailDao emailDao() {
    if (_emailDao != null) {
      return _emailDao;
    } else {
      synchronized(this) {
        if(_emailDao == null) {
          _emailDao = new EmailDao_Impl(this);
        }
        return _emailDao;
      }
    }
  }

  @Override
  public SubscriptionDao subscriptionDao() {
    if (_subscriptionDao != null) {
      return _subscriptionDao;
    } else {
      synchronized(this) {
        if(_subscriptionDao == null) {
          _subscriptionDao = new SubscriptionDao_Impl(this);
        }
        return _subscriptionDao;
      }
    }
  }
}
