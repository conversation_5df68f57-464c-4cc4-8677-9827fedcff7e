// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWorkerFactory;
import androidx.hilt.work.WorkerAssistedFactory;
import androidx.hilt.work.WorkerFactoryModule_ProvideFactoryFactory;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import androidx.work.ListenableWorker;
import androidx.work.WorkerParameters;
import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.remote.GmailApi;
import com.example.abonekaptanmobile.data.remote.HuggingFaceApi;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.EmailRepository;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import com.example.abonekaptanmobile.data.repository.GmailRepository;
import com.example.abonekaptanmobile.data.repository.HuggingFaceRepository;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import com.example.abonekaptanmobile.data.repository.SubscriptionRepository;
import com.example.abonekaptanmobile.di.AppModule_ProvideAppDatabaseFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideCommunityPatternDaoFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideCommunityPatternRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideEmailDaoFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideEmailRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideFeedbackDaoFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideFeedbackRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideGmailApiFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideGmailRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideGoogleAuthManagerFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideHuggingFaceApiFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideHuggingFaceRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideOkHttpClientFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideStageAnalysisDaoFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideStageAnalysisRepositoryFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideSubscriptionClassifierFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideSubscriptionDaoFactory;
import com.example.abonekaptanmobile.di.AppModule_ProvideSubscriptionRepositoryFactory;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import com.example.abonekaptanmobile.ui.viewmodel.MainViewModel;
import com.example.abonekaptanmobile.ui.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel;
import com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel_HiltModules_KeyModule_ProvideFactory;
import com.example.abonekaptanmobile.utils.HybridApproachTester;
import com.example.abonekaptanmobile.workers.ProcessFeedbackWorker;
import com.example.abonekaptanmobile.workers.ProcessFeedbackWorker_AssistedFactory;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import dagger.internal.SingleCheck;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerAboneKaptanApp_HiltComponents_SingletonC {
  private DaggerAboneKaptanApp_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public AboneKaptanApp_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements AboneKaptanApp_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements AboneKaptanApp_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements AboneKaptanApp_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements AboneKaptanApp_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements AboneKaptanApp_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements AboneKaptanApp_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements AboneKaptanApp_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public AboneKaptanApp_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends AboneKaptanApp_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends AboneKaptanApp_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends AboneKaptanApp_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends AboneKaptanApp_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(MainViewModel_HiltModules_KeyModule_ProvideFactory.provide(), StageAnalysisViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends AboneKaptanApp_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<StageAnalysisViewModel> stageAnalysisViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.stageAnalysisViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, Provider<ViewModel>>of("com.example.abonekaptanmobile.ui.viewmodel.MainViewModel", ((Provider) mainViewModelProvider), "com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel", ((Provider) stageAnalysisViewModelProvider));
    }

    @Override
    public Map<String, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<String, Object>of();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.abonekaptanmobile.ui.viewmodel.MainViewModel 
          return (T) new MainViewModel(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideGoogleAuthManagerProvider.get(), singletonCImpl.provideGmailRepositoryProvider.get(), singletonCImpl.provideEmailRepositoryProvider.get(), singletonCImpl.provideSubscriptionRepositoryProvider.get(), singletonCImpl.provideSubscriptionClassifierProvider.get(), singletonCImpl.provideFeedbackRepositoryProvider.get(), singletonCImpl.hybridApproachTesterProvider.get());

          case 1: // com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel 
          return (T) new StageAnalysisViewModel(singletonCImpl.provideStageAnalysisRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends AboneKaptanApp_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends AboneKaptanApp_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends AboneKaptanApp_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<FeedbackDao> provideFeedbackDaoProvider;

    private Provider<FeedbackRepository> provideFeedbackRepositoryProvider;

    private Provider<CommunityPatternDao> provideCommunityPatternDaoProvider;

    private Provider<CommunityPatternRepository> provideCommunityPatternRepositoryProvider;

    private Provider<ProcessFeedbackWorker_AssistedFactory> processFeedbackWorker_AssistedFactoryProvider;

    private Provider<GoogleAuthManager> provideGoogleAuthManagerProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<GmailApi> provideGmailApiProvider;

    private Provider<GmailRepository> provideGmailRepositoryProvider;

    private Provider<EmailDao> provideEmailDaoProvider;

    private Provider<EmailRepository> provideEmailRepositoryProvider;

    private Provider<SubscriptionDao> provideSubscriptionDaoProvider;

    private Provider<SubscriptionRepository> provideSubscriptionRepositoryProvider;

    private Provider<HuggingFaceApi> provideHuggingFaceApiProvider;

    private Provider<HuggingFaceRepository> provideHuggingFaceRepositoryProvider;

    private Provider<StageAnalysisDao> provideStageAnalysisDaoProvider;

    private Provider<StageAnalysisRepository> provideStageAnalysisRepositoryProvider;

    private Provider<SubscriptionClassifier> provideSubscriptionClassifierProvider;

    private Provider<HybridApproachTester> hybridApproachTesterProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private Map<String, Provider<WorkerAssistedFactory<? extends ListenableWorker>>> mapOfStringAndProviderOfWorkerAssistedFactoryOf(
        ) {
      return ImmutableMap.<String, Provider<WorkerAssistedFactory<? extends ListenableWorker>>>of("com.example.abonekaptanmobile.workers.ProcessFeedbackWorker", ((Provider) processFeedbackWorker_AssistedFactoryProvider));
    }

    private HiltWorkerFactory hiltWorkerFactory() {
      return WorkerFactoryModule_ProvideFactoryFactory.provideFactory(mapOfStringAndProviderOfWorkerAssistedFactoryOf());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 3));
      this.provideFeedbackDaoProvider = DoubleCheck.provider(new SwitchingProvider<FeedbackDao>(singletonCImpl, 2));
      this.provideFeedbackRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<FeedbackRepository>(singletonCImpl, 1));
      this.provideCommunityPatternDaoProvider = DoubleCheck.provider(new SwitchingProvider<CommunityPatternDao>(singletonCImpl, 5));
      this.provideCommunityPatternRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<CommunityPatternRepository>(singletonCImpl, 4));
      this.processFeedbackWorker_AssistedFactoryProvider = SingleCheck.provider(new SwitchingProvider<ProcessFeedbackWorker_AssistedFactory>(singletonCImpl, 0));
      this.provideGoogleAuthManagerProvider = DoubleCheck.provider(new SwitchingProvider<GoogleAuthManager>(singletonCImpl, 6));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 9));
      this.provideGmailApiProvider = DoubleCheck.provider(new SwitchingProvider<GmailApi>(singletonCImpl, 8));
      this.provideGmailRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<GmailRepository>(singletonCImpl, 7));
      this.provideEmailDaoProvider = DoubleCheck.provider(new SwitchingProvider<EmailDao>(singletonCImpl, 11));
      this.provideEmailRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<EmailRepository>(singletonCImpl, 10));
      this.provideSubscriptionDaoProvider = DoubleCheck.provider(new SwitchingProvider<SubscriptionDao>(singletonCImpl, 13));
      this.provideSubscriptionRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<SubscriptionRepository>(singletonCImpl, 12));
      this.provideHuggingFaceApiProvider = DoubleCheck.provider(new SwitchingProvider<HuggingFaceApi>(singletonCImpl, 16));
      this.provideHuggingFaceRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<HuggingFaceRepository>(singletonCImpl, 15));
      this.provideStageAnalysisDaoProvider = DoubleCheck.provider(new SwitchingProvider<StageAnalysisDao>(singletonCImpl, 18));
      this.provideStageAnalysisRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<StageAnalysisRepository>(singletonCImpl, 17));
      this.provideSubscriptionClassifierProvider = DoubleCheck.provider(new SwitchingProvider<SubscriptionClassifier>(singletonCImpl, 14));
      this.hybridApproachTesterProvider = DoubleCheck.provider(new SwitchingProvider<HybridApproachTester>(singletonCImpl, 19));
    }

    @Override
    public void injectAboneKaptanApp(AboneKaptanApp aboneKaptanApp) {
      injectAboneKaptanApp2(aboneKaptanApp);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private AboneKaptanApp injectAboneKaptanApp2(AboneKaptanApp instance) {
      AboneKaptanApp_MembersInjector.injectWorkerFactory(instance, hiltWorkerFactory());
      AboneKaptanApp_MembersInjector.injectCommunityPatternRepository(instance, provideCommunityPatternRepositoryProvider.get());
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.example.abonekaptanmobile.workers.ProcessFeedbackWorker_AssistedFactory 
          return (T) new ProcessFeedbackWorker_AssistedFactory() {
            @Override
            public ProcessFeedbackWorker create(Context appContext, WorkerParameters workerParams) {
              return new ProcessFeedbackWorker(appContext, workerParams, singletonCImpl.provideFeedbackRepositoryProvider.get(), singletonCImpl.provideCommunityPatternRepositoryProvider.get(), singletonCImpl.provideAppDatabaseProvider.get());
            }
          };

          case 1: // com.example.abonekaptanmobile.data.repository.FeedbackRepository 
          return (T) AppModule_ProvideFeedbackRepositoryFactory.provideFeedbackRepository(singletonCImpl.provideFeedbackDaoProvider.get());

          case 2: // com.example.abonekaptanmobile.data.local.dao.FeedbackDao 
          return (T) AppModule_ProvideFeedbackDaoFactory.provideFeedbackDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 3: // com.example.abonekaptanmobile.data.local.AppDatabase 
          return (T) AppModule_ProvideAppDatabaseFactory.provideAppDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.example.abonekaptanmobile.data.repository.CommunityPatternRepository 
          return (T) AppModule_ProvideCommunityPatternRepositoryFactory.provideCommunityPatternRepository(singletonCImpl.provideCommunityPatternDaoProvider.get());

          case 5: // com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao 
          return (T) AppModule_ProvideCommunityPatternDaoFactory.provideCommunityPatternDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 6: // com.example.abonekaptanmobile.auth.GoogleAuthManager 
          return (T) AppModule_ProvideGoogleAuthManagerFactory.provideGoogleAuthManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.example.abonekaptanmobile.data.repository.GmailRepository 
          return (T) AppModule_ProvideGmailRepositoryFactory.provideGmailRepository(singletonCImpl.provideGmailApiProvider.get());

          case 8: // com.example.abonekaptanmobile.data.remote.GmailApi 
          return (T) AppModule_ProvideGmailApiFactory.provideGmailApi(singletonCImpl.provideOkHttpClientProvider.get());

          case 9: // okhttp3.OkHttpClient 
          return (T) AppModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.provideGoogleAuthManagerProvider.get());

          case 10: // com.example.abonekaptanmobile.data.repository.EmailRepository 
          return (T) AppModule_ProvideEmailRepositoryFactory.provideEmailRepository(singletonCImpl.provideEmailDaoProvider.get());

          case 11: // com.example.abonekaptanmobile.data.local.dao.EmailDao 
          return (T) AppModule_ProvideEmailDaoFactory.provideEmailDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 12: // com.example.abonekaptanmobile.data.repository.SubscriptionRepository 
          return (T) AppModule_ProvideSubscriptionRepositoryFactory.provideSubscriptionRepository(singletonCImpl.provideSubscriptionDaoProvider.get());

          case 13: // com.example.abonekaptanmobile.data.local.dao.SubscriptionDao 
          return (T) AppModule_ProvideSubscriptionDaoFactory.provideSubscriptionDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 14: // com.example.abonekaptanmobile.services.SubscriptionClassifier 
          return (T) AppModule_ProvideSubscriptionClassifierFactory.provideSubscriptionClassifier(singletonCImpl.provideCommunityPatternRepositoryProvider.get(), singletonCImpl.provideHuggingFaceRepositoryProvider.get(), singletonCImpl.provideStageAnalysisRepositoryProvider.get());

          case 15: // com.example.abonekaptanmobile.data.repository.HuggingFaceRepository 
          return (T) AppModule_ProvideHuggingFaceRepositoryFactory.provideHuggingFaceRepository(singletonCImpl.provideHuggingFaceApiProvider.get());

          case 16: // com.example.abonekaptanmobile.data.remote.HuggingFaceApi 
          return (T) AppModule_ProvideHuggingFaceApiFactory.provideHuggingFaceApi();

          case 17: // com.example.abonekaptanmobile.data.repository.StageAnalysisRepository 
          return (T) AppModule_ProvideStageAnalysisRepositoryFactory.provideStageAnalysisRepository(singletonCImpl.provideStageAnalysisDaoProvider.get());

          case 18: // com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao 
          return (T) AppModule_ProvideStageAnalysisDaoFactory.provideStageAnalysisDao(singletonCImpl.provideAppDatabaseProvider.get());

          case 19: // com.example.abonekaptanmobile.utils.HybridApproachTester 
          return (T) new HybridApproachTester(singletonCImpl.provideHuggingFaceRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
