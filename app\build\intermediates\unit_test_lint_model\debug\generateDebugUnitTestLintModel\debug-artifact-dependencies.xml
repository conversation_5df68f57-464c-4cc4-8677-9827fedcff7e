<dependencies>
  <compile
      roots=":@@:app::debug,androidx.hilt:hilt-navigation-compose:1.1.0@aar,com.google.android.gms:play-services-auth:21.0.0@aar,androidx.hilt:hilt-work:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,com.google.dagger:hilt-android:2.49@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-tasks:18.0.1@aar,com.google.android.gms:play-services-basement:18.2.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.loader:loader:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.navigation:navigation-compose:2.5.1@aar,androidx.navigation:navigation-runtime-ktx:2.5.1@aar,androidx.navigation:navigation-runtime:2.5.1@aar,androidx.navigation:navigation-common-ktx:2.5.1@aar,androidx.navigation:navigation-common:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar,androidx.compose.material3:material3-android:1.2.0@aar,androidx.compose.ui:ui-util-android:1.6.2@aar,androidx.compose.ui:ui-unit-android:1.6.2@aar,androidx.compose.ui:ui-text-android:1.6.2@aar,androidx.compose.foundation:foundation-layout-android:1.6.2@aar,androidx.compose.material:material-icons-extended-android:1.6.2@aar,androidx.compose.material:material-icons-core-android:1.6.2@aar,androidx.compose.material:material-ripple-android:1.6.2@aar,androidx.compose.foundation:foundation-android:1.6.2@aar,androidx.compose.animation:animation-core-android:1.6.2@aar,androidx.compose.animation:animation-android:1.6.2@aar,androidx.compose.ui:ui-geometry-android:1.6.2@aar,androidx.compose.ui:ui-tooling-data-android:1.6.2@aar,androidx.compose.ui:ui-tooling-preview-android:1.6.2@aar,androidx.compose.ui:ui-graphics-android:1.6.2@aar,androidx.compose.ui:ui-android:1.6.2@aar,androidx.compose.ui:ui-tooling-android:1.6.2@aar,androidx.compose.ui:ui-test-manifest:1.6.2@aar,androidx.activity:activity:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.core:core-ktx:1.12.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,androidx.compose.runtime:runtime-saveable-android:1.6.2@aar,androidx.compose.runtime:runtime-android:1.6.2@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.collection:collection-jvm:1.4.0@jar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar,com.google.api-client:google-api-client-android:2.2.0@jar,com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0@jar,com.google.auth:google-auth-library-oauth2-http:1.23.0@jar,com.google.auth:google-auth-library-credentials:1.23.0@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,com.google.api-client:google-api-client:2.0.0@jar,com.google.oauth-client:google-oauth-client:1.34.1@jar,com.google.http-client:google-http-client-gson:1.43.3@jar,com.google.http-client:google-http-client-apache-v2:1.42.1@jar,com.google.http-client:google-http-client:1.43.3@jar,io.opencensus:opencensus-contrib-http-util:0.31.1@jar,com.google.guava:guava:32.0.0-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,com.google.code.gson:gson:2.10.1@jar,org.apache.httpcomponents:httpclient:4.5.14@jar,org.apache.httpcomponents:httpcore:4.4.16@jar,commons-logging:commons-logging:1.2@jar,commons-codec:commons-codec:1.11@jar,com.google.dagger:hilt-core:2.49@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.errorprone:error_prone_annotations:2.18.0@jar,com.google.guava:failureaccess:1.0.1@jar,org.checkerframework:checker-qual:3.33.0@jar,com.google.j2objc:j2objc-annotations:2.8@jar,io.opencensus:opencensus-api:0.31.1@jar,io.grpc:grpc-context:1.27.2@jar,com.google.auto.value:auto-value-annotations:1.10.4@jar,com.google.dagger:dagger:2.49@jar,javax.inject:javax.inject:1@jar,com.google.dagger:dagger-lint-aar:2.49@aar,androidx.hilt:hilt-common:1.1.0@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.hilt:hilt-work:1.1.0@aar"
        simpleName="androidx.hilt:hilt-work"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="com.google.dagger:hilt-android:2.49@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.5.1@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.5.1@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.5.1@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.5.1@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-common:2.5.1@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.6.2@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.6.2@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.6.2@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.6.2@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.6.2@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.6.2@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.api-client:google-api-client-android:2.2.0@jar"
        simpleName="com.google.api-client:google-api-client-android"/>
    <dependency
        name="com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0@jar"
        simpleName="com.google.apis:google-api-services-gmail"/>
    <dependency
        name="com.google.auth:google-auth-library-oauth2-http:1.23.0@jar"
        simpleName="com.google.auth:google-auth-library-oauth2-http"/>
    <dependency
        name="com.google.auth:google-auth-library-credentials:1.23.0@jar"
        simpleName="com.google.auth:google-auth-library-credentials"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.api-client:google-api-client:2.0.0@jar"
        simpleName="com.google.api-client:google-api-client"/>
    <dependency
        name="com.google.oauth-client:google-oauth-client:1.34.1@jar"
        simpleName="com.google.oauth-client:google-oauth-client"/>
    <dependency
        name="com.google.http-client:google-http-client-gson:1.43.3@jar"
        simpleName="com.google.http-client:google-http-client-gson"/>
    <dependency
        name="com.google.http-client:google-http-client-apache-v2:1.42.1@jar"
        simpleName="com.google.http-client:google-http-client-apache-v2"/>
    <dependency
        name="com.google.http-client:google-http-client:1.43.3@jar"
        simpleName="com.google.http-client:google-http-client"/>
    <dependency
        name="io.opencensus:opencensus-contrib-http-util:0.31.1@jar"
        simpleName="io.opencensus:opencensus-contrib-http-util"/>
    <dependency
        name="com.google.guava:guava:32.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="org.apache.httpcomponents:httpclient:4.5.14@jar"
        simpleName="org.apache.httpcomponents:httpclient"/>
    <dependency
        name="org.apache.httpcomponents:httpcore:4.4.16@jar"
        simpleName="org.apache.httpcomponents:httpcore"/>
    <dependency
        name="commons-logging:commons-logging:1.2@jar"
        simpleName="commons-logging:commons-logging"/>
    <dependency
        name="commons-codec:commons-codec:1.11@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.google.dagger:hilt-core:2.49@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.18.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="org.checkerframework:checker-qual:3.33.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="io.opencensus:opencensus-api:0.31.1@jar"
        simpleName="io.opencensus:opencensus-api"/>
    <dependency
        name="io.grpc:grpc-context:1.27.2@jar"
        simpleName="io.grpc:grpc-context"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.10.4@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.dagger:dagger:2.49@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.49@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="androidx.hilt:hilt-common:1.1.0@jar"
        simpleName="androidx.hilt:hilt-common"/>
  </compile>
  <package
      roots="junit:junit:4.13.2@jar,com.google.android.gms:play-services-auth:21.0.0@aar,androidx.hilt:hilt-work:1.1.0@aar,androidx.hilt:hilt-navigation-compose:1.1.0@aar,androidx.hilt:hilt-navigation:1.1.0@aar,com.google.dagger:hilt-android:2.49@aar,com.google.android.gms:play-services-auth-api-phone:18.0.2@aar,com.google.android.gms:play-services-auth-base:18.0.10@aar,com.google.android.gms:play-services-fido:20.0.1@aar,com.google.android.gms:play-services-base:18.0.1@aar,com.google.android.gms:play-services-tasks:18.0.1@aar,com.google.android.gms:play-services-basement:18.2.0@aar,androidx.fragment:fragment:1.5.7@aar,androidx.fragment:fragment:1.5.7@aar,androidx.loader:loader:1.1.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.navigation:navigation-compose:2.5.1@aar,androidx.compose.material3:material3-android:1.2.0@aar,androidx.compose.material:material-android:1.6.2@aar,androidx.compose.material:material-icons-core-android:1.6.2@aar,androidx.compose.material:material-icons-extended-android:1.6.2@aar,androidx.compose.material:material-ripple-android:1.6.2@aar,androidx.compose.foundation:foundation-android:1.6.2@aar,androidx.compose.foundation:foundation-layout-android:1.6.2@aar,androidx.compose.animation:animation-core-android:1.6.2@aar,androidx.compose.animation:animation-android:1.6.2@aar,androidx.compose.ui:ui-tooling-data-android:1.6.2@aar,androidx.compose.ui:ui-unit-android:1.6.2@aar,androidx.compose.ui:ui-geometry-android:1.6.2@aar,androidx.compose.ui:ui-util-android:1.6.2@aar,androidx.compose.ui:ui-tooling-preview-android:1.6.2@aar,androidx.compose.ui:ui-tooling-android:1.6.2@aar,androidx.compose.ui:ui-graphics-android:1.6.2@aar,androidx.compose.ui:ui-test-manifest:1.6.2@aar,androidx.compose.ui:ui-text-android:1.6.2@aar,androidx.emoji2:emoji2:1.3.0@aar,androidx.lifecycle:lifecycle-process:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-service:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-common-java8:2.7.0@jar,androidx.navigation:navigation-runtime-ktx:2.5.1@aar,androidx.navigation:navigation-runtime:2.5.1@aar,androidx.navigation:navigation-common-ktx:2.5.1@aar,androidx.navigation:navigation-common:2.5.1@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar,androidx.compose.ui:ui-android:1.6.2@aar,androidx.activity:activity-ktx:1.8.2@aar,androidx.activity:activity-compose:1.8.2@aar,androidx.activity:activity:1.8.2@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.customview:customview:1.0.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.room:room-common:2.6.1@jar,androidx.room:room-runtime:2.6.1@aar,androidx.room:room-ktx:2.6.1@aar,androidx.sqlite:sqlite-framework:2.4.0@aar,androidx.sqlite:sqlite:2.4.0@aar,androidx.compose.runtime:runtime-saveable-android:1.6.2@aar,androidx.compose.runtime:runtime-android:1.6.2@aar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.retrofit2:converter-gson:2.9.0@jar,com.squareup.retrofit2:retrofit:2.9.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection-ktx:1.4.0@jar,androidx.collection:collection-jvm:1.4.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.profileinstaller:profileinstaller:1.3.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar,com.google.api-client:google-api-client-android:2.2.0@jar,com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0@jar,com.google.auth:google-auth-library-oauth2-http:1.23.0@jar,com.google.auth:google-auth-library-credentials:1.23.0@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,com.google.api-client:google-api-client:2.0.0@jar,com.google.oauth-client:google-oauth-client:1.34.1@jar,com.google.http-client:google-http-client-gson:1.43.3@jar,com.google.code.gson:gson:2.10.1@jar,com.google.http-client:google-http-client-apache-v2:1.42.1@jar,com.google.http-client:google-http-client:1.43.3@jar,com.google.auto.value:auto-value-annotations:1.10.4@jar,io.opencensus:opencensus-contrib-http-util:0.31.1@jar,com.google.guava:guava:32.0.0-android@jar,androidx.hilt:hilt-common:1.1.0@jar,com.google.dagger:hilt-core:2.49@jar,com.google.code.findbugs:jsr305:3.0.2@jar,com.google.errorprone:error_prone_annotations:2.18.0@jar,com.google.dagger:dagger:2.49@jar,com.google.dagger:dagger-lint-aar:2.49@aar,javax.inject:javax.inject:1@jar,org.apache.httpcomponents:httpclient:4.5.14@jar,org.apache.httpcomponents:httpcore:4.4.16@jar,com.google.j2objc:j2objc-annotations:2.8@jar,io.opencensus:opencensus-api:0.31.1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.checkerframework:checker-qual:3.33.0@jar,commons-logging:commons-logging:1.2@jar,commons-codec:commons-codec:1.11@jar,io.grpc:grpc-context:1.27.2@jar">
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="com.google.android.gms:play-services-auth:21.0.0@aar"
        simpleName="com.google.android.gms:play-services-auth"/>
    <dependency
        name="androidx.hilt:hilt-work:1.1.0@aar"
        simpleName="androidx.hilt:hilt-work"/>
    <dependency
        name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation-compose"/>
    <dependency
        name="androidx.hilt:hilt-navigation:1.1.0@aar"
        simpleName="androidx.hilt:hilt-navigation"/>
    <dependency
        name="com.google.dagger:hilt-android:2.49@aar"
        simpleName="com.google.dagger:hilt-android"/>
    <dependency
        name="com.google.android.gms:play-services-auth-api-phone:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-auth-api-phone"/>
    <dependency
        name="com.google.android.gms:play-services-auth-base:18.0.10@aar"
        simpleName="com.google.android.gms:play-services-auth-base"/>
    <dependency
        name="com.google.android.gms:play-services-fido:20.0.1@aar"
        simpleName="com.google.android.gms:play-services-fido"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.1@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.7@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.loader:loader:1.1.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.navigation:navigation-compose:2.5.1@aar"
        simpleName="androidx.navigation:navigation-compose"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.0@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.material:material-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-icons-extended-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-icons-extended-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.2@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.6.2@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.6.2@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.6.2@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.6.2@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-data-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-data-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-tooling-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-test-manifest:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-test-manifest"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.emoji2:emoji2:1.3.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.navigation:navigation-runtime-ktx:2.5.1@aar"
        simpleName="androidx.navigation:navigation-runtime-ktx"/>
    <dependency
        name="androidx.navigation:navigation-runtime:2.5.1@aar"
        simpleName="androidx.navigation:navigation-runtime"/>
    <dependency
        name="androidx.navigation:navigation-common-ktx:2.5.1@aar"
        simpleName="androidx.navigation:navigation-common-ktx"/>
    <dependency
        name="androidx.navigation:navigation-common:2.5.1@aar"
        simpleName="androidx.navigation:navigation-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-compose"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-compose"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.6.2@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.2@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.8.2@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity:1.8.2@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.customview:customview:1.0.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.room:room-common:2.6.1@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.room:room-runtime:2.6.1@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.room:room-ktx:2.6.1@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.sqlite:sqlite:2.4.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.6.2@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.6.2@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.9.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.api-client:google-api-client-android:2.2.0@jar"
        simpleName="com.google.api-client:google-api-client-android"/>
    <dependency
        name="com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0@jar"
        simpleName="com.google.apis:google-api-services-gmail"/>
    <dependency
        name="com.google.auth:google-auth-library-oauth2-http:1.23.0@jar"
        simpleName="com.google.auth:google-auth-library-oauth2-http"/>
    <dependency
        name="com.google.auth:google-auth-library-credentials:1.23.0@jar"
        simpleName="com.google.auth:google-auth-library-credentials"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.api-client:google-api-client:2.0.0@jar"
        simpleName="com.google.api-client:google-api-client"/>
    <dependency
        name="com.google.oauth-client:google-oauth-client:1.34.1@jar"
        simpleName="com.google.oauth-client:google-oauth-client"/>
    <dependency
        name="com.google.http-client:google-http-client-gson:1.43.3@jar"
        simpleName="com.google.http-client:google-http-client-gson"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.http-client:google-http-client-apache-v2:1.42.1@jar"
        simpleName="com.google.http-client:google-http-client-apache-v2"/>
    <dependency
        name="com.google.http-client:google-http-client:1.43.3@jar"
        simpleName="com.google.http-client:google-http-client"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.10.4@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="io.opencensus:opencensus-contrib-http-util:0.31.1@jar"
        simpleName="io.opencensus:opencensus-contrib-http-util"/>
    <dependency
        name="com.google.guava:guava:32.0.0-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="androidx.hilt:hilt-common:1.1.0@jar"
        simpleName="androidx.hilt:hilt-common"/>
    <dependency
        name="com.google.dagger:hilt-core:2.49@jar"
        simpleName="com.google.dagger:hilt-core"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.18.0@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.dagger:dagger:2.49@jar"
        simpleName="com.google.dagger:dagger"/>
    <dependency
        name="com.google.dagger:dagger-lint-aar:2.49@aar"
        simpleName="com.google.dagger:dagger-lint-aar"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.apache.httpcomponents:httpclient:4.5.14@jar"
        simpleName="org.apache.httpcomponents:httpclient"/>
    <dependency
        name="org.apache.httpcomponents:httpcore:4.4.16@jar"
        simpleName="org.apache.httpcomponents:httpcore"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
    <dependency
        name="io.opencensus:opencensus-api:0.31.1@jar"
        simpleName="io.opencensus:opencensus-api"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.checkerframework:checker-qual:3.33.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="commons-logging:commons-logging:1.2@jar"
        simpleName="commons-logging:commons-logging"/>
    <dependency
        name="commons-codec:commons-codec:1.11@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="io.grpc:grpc-context:1.27.2@jar"
        simpleName="io.grpc:grpc-context"/>
  </package>
</dependencies>
