// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EmailRepository_Factory implements Factory<EmailRepository> {
  private final Provider<EmailDao> emailDaoProvider;

  public EmailRepository_Factory(Provider<EmailDao> emailDaoProvider) {
    this.emailDaoProvider = emailDaoProvider;
  }

  @Override
  public EmailRepository get() {
    return newInstance(emailDaoProvider.get());
  }

  public static EmailRepository_Factory create(Provider<EmailDao> emailDaoProvider) {
    return new EmailRepository_Factory(emailDaoProvider);
  }

  public static EmailRepository newInstance(EmailDao emailDao) {
    return new EmailRepository(emailDao);
  }
}
