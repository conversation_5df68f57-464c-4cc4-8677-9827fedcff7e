  Activity android.app  Application android.app  Bundle android.app.Activity  ExperimentalMaterial3Api android.app.Activity  OptIn android.app.Activity  CommunityPatternRepository android.app.Application  
Configuration android.app.Application  HiltWorkerFactory android.app.Application  Inject android.app.Application  Context android.content  Intent android.content  Bundle android.content.Context  CommunityPatternRepository android.content.Context  
Configuration android.content.Context  ExperimentalMaterial3Api android.content.Context  HiltWorkerFactory android.content.Context  Inject android.content.Context  OptIn android.content.Context  Bundle android.content.ContextWrapper  CommunityPatternRepository android.content.ContextWrapper  
Configuration android.content.ContextWrapper  ExperimentalMaterial3Api android.content.ContextWrapper  HiltWorkerFactory android.content.ContextWrapper  Inject android.content.ContextWrapper  OptIn android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  Base64 android.util  Log android.util  w android.util.Log  Bundle  android.view.ContextThemeWrapper  ExperimentalMaterial3Api  android.view.ContextThemeWrapper  OptIn  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  ExperimentalMaterial3Api #androidx.activity.ComponentActivity  OptIn #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  ActivityResultContracts !androidx.activity.result.contract  ExperimentalFoundationApi androidx.compose.foundation  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  
Composable "androidx.compose.foundation.layout  ExperimentalFoundationApi "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
selectable %androidx.compose.foundation.selection  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Close &androidx.compose.material.icons.filled  	ExitToApp &androidx.compose.material.icons.filled  Login &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Science &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalFoundationApi androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ExperimentalFoundationApi androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
SideEffect androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  ExperimentalMaterial3Api #androidx.core.app.ComponentActivity  OptIn #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  
hiltViewModel  androidx.hilt.navigation.compose  
HiltWorker androidx.hilt.work  HiltWorkerFactory androidx.hilt.work  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  ActivityResultLauncher androidx.lifecycle.ViewModel  ApplicationContext androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  EmailRepository androidx.lifecycle.ViewModel  FeedbackRepository androidx.lifecycle.ViewModel  Float androidx.lifecycle.ViewModel  GmailRepository androidx.lifecycle.ViewModel  GoogleAuthManager androidx.lifecycle.ViewModel  GoogleSignInAccount androidx.lifecycle.ViewModel  HybridApproachTester androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Intent androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  SubscriptionClassifier androidx.lifecycle.ViewModel  SubscriptionItem androidx.lifecycle.ViewModel  SubscriptionRepository androidx.lifecycle.ViewModel  SubscriptionStatus androidx.lifecycle.ViewModel  Task androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Index 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  Upsert 
androidx.room  withTransaction 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  CommunityPatternDao androidx.room.RoomDatabase  EmailDao androidx.room.RoomDatabase  	Exception androidx.room.RoomDatabase  FeedbackDao androidx.room.RoomDatabase  Log androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  PatternType androidx.room.RoomDatabase  SubscriptionDao androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  
trimIndent androidx.room.RoomDatabase  Log $androidx.room.RoomDatabase.Companion  PatternType $androidx.room.RoomDatabase.Companion  
getTRIMIndent $androidx.room.RoomDatabase.Companion  
getTrimIndent $androidx.room.RoomDatabase.Companion  
trimIndent $androidx.room.RoomDatabase.Companion  	Migration androidx.room.migration  	Exception !androidx.room.migration.Migration  Log !androidx.room.migration.Migration  PatternType !androidx.room.migration.Migration  SupportSQLiteDatabase !androidx.room.migration.Migration  
trimIndent !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  execSQL (androidx.sqlite.db.SupportSQLiteDatabase  
Configuration 
androidx.work  CoroutineWorker 
androidx.work  MutableStateFlow 
androidx.work  	StateFlow 
androidx.work  WorkerParameters 
androidx.work  asStateFlow 
androidx.work  	emptyList 
androidx.work  Builder androidx.work.Configuration  Provider androidx.work.Configuration  build #androidx.work.Configuration.Builder  setWorkerFactory #androidx.work.Configuration.Builder  Builder %androidx.work.Configuration.Companion  AppDatabase androidx.work.CoroutineWorker  Assisted androidx.work.CoroutineWorker  AssistedInject androidx.work.CoroutineWorker  CommunityPatternRepository androidx.work.CoroutineWorker  Context androidx.work.CoroutineWorker  FeedbackRepository androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  AppDatabase androidx.work.ListenableWorker  Assisted androidx.work.ListenableWorker  AssistedInject androidx.work.ListenableWorker  CommunityPatternRepository androidx.work.ListenableWorker  Context androidx.work.ListenableWorker  FeedbackRepository androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  WorkerParameters androidx.work.ListenableWorker  AboneKaptanApp com.example.abonekaptanmobile  
Configuration com.example.abonekaptanmobile  ExperimentalMaterial3Api com.example.abonekaptanmobile  MainActivity com.example.abonekaptanmobile  OptIn com.example.abonekaptanmobile  R com.example.abonekaptanmobile  CommunityPatternRepository ,com.example.abonekaptanmobile.AboneKaptanApp  
Configuration ,com.example.abonekaptanmobile.AboneKaptanApp  HiltWorkerFactory ,com.example.abonekaptanmobile.AboneKaptanApp  Inject ,com.example.abonekaptanmobile.AboneKaptanApp  
workerFactory ,com.example.abonekaptanmobile.AboneKaptanApp  Bundle *com.example.abonekaptanmobile.MainActivity  ExperimentalMaterial3Api *com.example.abonekaptanmobile.MainActivity  OptIn *com.example.abonekaptanmobile.MainActivity  GmailScopes "com.example.abonekaptanmobile.auth  GoogleAuthManager "com.example.abonekaptanmobile.auth  GoogleSignIn "com.example.abonekaptanmobile.auth  GoogleSignInOptions "com.example.abonekaptanmobile.auth  Scope "com.example.abonekaptanmobile.auth  Void "com.example.abonekaptanmobile.auth  com "com.example.abonekaptanmobile.auth  ActivityResultLauncher 4com.example.abonekaptanmobile.auth.GoogleAuthManager  ApplicationContext 4com.example.abonekaptanmobile.auth.GoogleAuthManager  Context 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GmailScopes 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GoogleAccountCredential 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GoogleSignIn 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GoogleSignInAccount 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GoogleSignInClient 4com.example.abonekaptanmobile.auth.GoogleAuthManager  GoogleSignInOptions 4com.example.abonekaptanmobile.auth.GoogleAuthManager  Inject 4com.example.abonekaptanmobile.auth.GoogleAuthManager  Intent 4com.example.abonekaptanmobile.auth.GoogleAuthManager  Scope 4com.example.abonekaptanmobile.auth.GoogleAuthManager  Void 4com.example.abonekaptanmobile.auth.GoogleAuthManager  com 4com.example.abonekaptanmobile.auth.GoogleAuthManager  context 4com.example.abonekaptanmobile.auth.GoogleAuthManager  getCurrentAccount 4com.example.abonekaptanmobile.auth.GoogleAuthManager  gso 4com.example.abonekaptanmobile.auth.GoogleAuthManager  AppDatabase (com.example.abonekaptanmobile.data.local  EmailEntity (com.example.abonekaptanmobile.data.local  	Exception (com.example.abonekaptanmobile.data.local  FeedbackEntity (com.example.abonekaptanmobile.data.local  Log (com.example.abonekaptanmobile.data.local  PatternType (com.example.abonekaptanmobile.data.local  SubscriptionEntity (com.example.abonekaptanmobile.data.local  SubscriptionPatternEntity (com.example.abonekaptanmobile.data.local  
trimIndent (com.example.abonekaptanmobile.data.local  CommunityPatternDao 4com.example.abonekaptanmobile.data.local.AppDatabase  EmailDao 4com.example.abonekaptanmobile.data.local.AppDatabase  	Exception 4com.example.abonekaptanmobile.data.local.AppDatabase  FeedbackDao 4com.example.abonekaptanmobile.data.local.AppDatabase  Log 4com.example.abonekaptanmobile.data.local.AppDatabase  	Migration 4com.example.abonekaptanmobile.data.local.AppDatabase  PatternType 4com.example.abonekaptanmobile.data.local.AppDatabase  SubscriptionDao 4com.example.abonekaptanmobile.data.local.AppDatabase  SupportSQLiteDatabase 4com.example.abonekaptanmobile.data.local.AppDatabase  
trimIndent 4com.example.abonekaptanmobile.data.local.AppDatabase  CommunityPatternDao >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  EmailDao >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  	Exception >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  FeedbackDao >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  Log >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  	Migration >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  PatternType >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  SubscriptionDao >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  SupportSQLiteDatabase >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  
getTRIMIndent >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  
getTrimIndent >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  
trimIndent >com.example.abonekaptanmobile.data.local.AppDatabase.Companion  
getTRIMIndent _com.example.abonekaptanmobile.data.local.AppDatabase.Companion.MIGRATION_3_4.<no name provided>  
getTrimIndent _com.example.abonekaptanmobile.data.local.AppDatabase.Companion.MIGRATION_3_4.<no name provided>  CommunityPatternDao ,com.example.abonekaptanmobile.data.local.dao  Dao ,com.example.abonekaptanmobile.data.local.dao  Delete ,com.example.abonekaptanmobile.data.local.dao  EmailDao ,com.example.abonekaptanmobile.data.local.dao  FeedbackDao ,com.example.abonekaptanmobile.data.local.dao  Insert ,com.example.abonekaptanmobile.data.local.dao  Int ,com.example.abonekaptanmobile.data.local.dao  List ,com.example.abonekaptanmobile.data.local.dao  Long ,com.example.abonekaptanmobile.data.local.dao  OnConflictStrategy ,com.example.abonekaptanmobile.data.local.dao  Query ,com.example.abonekaptanmobile.data.local.dao  String ,com.example.abonekaptanmobile.data.local.dao  SubscriptionDao ,com.example.abonekaptanmobile.data.local.dao  Update ,com.example.abonekaptanmobile.data.local.dao  Int @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  List @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  Query @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  String @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  SubscriptionPatternEntity @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  Upsert @com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao  Delete 5com.example.abonekaptanmobile.data.local.dao.EmailDao  EmailEntity 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Flow 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Insert 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Int 5com.example.abonekaptanmobile.data.local.dao.EmailDao  List 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Long 5com.example.abonekaptanmobile.data.local.dao.EmailDao  OnConflictStrategy 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Query 5com.example.abonekaptanmobile.data.local.dao.EmailDao  String 5com.example.abonekaptanmobile.data.local.dao.EmailDao  Update 5com.example.abonekaptanmobile.data.local.dao.EmailDao  FeedbackEntity 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  Insert 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  List 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  Long 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  OnConflictStrategy 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  Query 8com.example.abonekaptanmobile.data.local.dao.FeedbackDao  Delete <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Flow <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Insert <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Int <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  List <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  OnConflictStrategy <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Query <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  String <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  SubscriptionEntity <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Update <com.example.abonekaptanmobile.data.local.dao.SubscriptionDao  Boolean /com.example.abonekaptanmobile.data.local.entity  EmailEntity /com.example.abonekaptanmobile.data.local.entity  FeedbackEntity /com.example.abonekaptanmobile.data.local.entity  Int /com.example.abonekaptanmobile.data.local.entity  Long /com.example.abonekaptanmobile.data.local.entity  PatternType /com.example.abonekaptanmobile.data.local.entity  String /com.example.abonekaptanmobile.data.local.entity  SubscriptionEntity /com.example.abonekaptanmobile.data.local.entity  SubscriptionPatternEntity /com.example.abonekaptanmobile.data.local.entity  Long ;com.example.abonekaptanmobile.data.local.entity.EmailEntity  
PrimaryKey ;com.example.abonekaptanmobile.data.local.entity.EmailEntity  String ;com.example.abonekaptanmobile.data.local.entity.EmailEntity  Boolean >com.example.abonekaptanmobile.data.local.entity.FeedbackEntity  Long >com.example.abonekaptanmobile.data.local.entity.FeedbackEntity  
PrimaryKey >com.example.abonekaptanmobile.data.local.entity.FeedbackEntity  String >com.example.abonekaptanmobile.data.local.entity.FeedbackEntity  UNKNOWN ;com.example.abonekaptanmobile.data.local.entity.PatternType  Int Bcom.example.abonekaptanmobile.data.local.entity.SubscriptionEntity  Long Bcom.example.abonekaptanmobile.data.local.entity.SubscriptionEntity  
PrimaryKey Bcom.example.abonekaptanmobile.data.local.entity.SubscriptionEntity  String Bcom.example.abonekaptanmobile.data.local.entity.SubscriptionEntity  Boolean Icom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity  Int Icom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity  Long Icom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity  
PrimaryKey Icom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity  String Icom.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity  GmailApi )com.example.abonekaptanmobile.data.remote  HuggingFaceApi )com.example.abonekaptanmobile.data.remote  Int )com.example.abonekaptanmobile.data.remote  List )com.example.abonekaptanmobile.data.remote  String )com.example.abonekaptanmobile.data.remote  GET 2com.example.abonekaptanmobile.data.remote.GmailApi  GmailMessage 2com.example.abonekaptanmobile.data.remote.GmailApi  GmailMessageListResponse 2com.example.abonekaptanmobile.data.remote.GmailApi  Int 2com.example.abonekaptanmobile.data.remote.GmailApi  List 2com.example.abonekaptanmobile.data.remote.GmailApi  Path 2com.example.abonekaptanmobile.data.remote.GmailApi  Query 2com.example.abonekaptanmobile.data.remote.GmailApi  String 2com.example.abonekaptanmobile.data.remote.GmailApi  Body 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  Header 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  HuggingFaceRequest 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  HuggingFaceResponse 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi   HuggingFaceTextGenerationRequest 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  !HuggingFaceTextGenerationResponse 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  POST 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  String 8com.example.abonekaptanmobile.data.remote.HuggingFaceApi  Boolean /com.example.abonekaptanmobile.data.remote.model  ClassificationResult /com.example.abonekaptanmobile.data.remote.model  DetailedClassificationResult /com.example.abonekaptanmobile.data.remote.model  FinalSubscriptionStatus /com.example.abonekaptanmobile.data.remote.model  Float /com.example.abonekaptanmobile.data.remote.model  GmailMessage /com.example.abonekaptanmobile.data.remote.model  GmailMessageListResponse /com.example.abonekaptanmobile.data.remote.model  HuggingFaceParameters /com.example.abonekaptanmobile.data.remote.model  HuggingFaceRequest /com.example.abonekaptanmobile.data.remote.model  HuggingFaceResponse /com.example.abonekaptanmobile.data.remote.model  #HuggingFaceTextGenerationParameters /com.example.abonekaptanmobile.data.remote.model   HuggingFaceTextGenerationRequest /com.example.abonekaptanmobile.data.remote.model  !HuggingFaceTextGenerationResponse /com.example.abonekaptanmobile.data.remote.model  HybridValidationResult /com.example.abonekaptanmobile.data.remote.model  Int /com.example.abonekaptanmobile.data.remote.model  List /com.example.abonekaptanmobile.data.remote.model  LlamaParameters /com.example.abonekaptanmobile.data.remote.model  LlamaRequest /com.example.abonekaptanmobile.data.remote.model  
LlamaResponse /com.example.abonekaptanmobile.data.remote.model  Long /com.example.abonekaptanmobile.data.remote.model  
MessageHeader /com.example.abonekaptanmobile.data.remote.model  	MessageId /com.example.abonekaptanmobile.data.remote.model  MessagePartBody /com.example.abonekaptanmobile.data.remote.model  MessagePayload /com.example.abonekaptanmobile.data.remote.model  ReplicateInput /com.example.abonekaptanmobile.data.remote.model  String /com.example.abonekaptanmobile.data.remote.model  TwoStageAnalysisResult /com.example.abonekaptanmobile.data.remote.model  joinToString /com.example.abonekaptanmobile.data.remote.model  ClassificationResult Dcom.example.abonekaptanmobile.data.remote.model.ClassificationResult  Float Dcom.example.abonekaptanmobile.data.remote.model.ClassificationResult  HuggingFaceResponse Dcom.example.abonekaptanmobile.data.remote.model.ClassificationResult  List Dcom.example.abonekaptanmobile.data.remote.model.ClassificationResult  String Dcom.example.abonekaptanmobile.data.remote.model.ClassificationResult  ClassificationResult Ncom.example.abonekaptanmobile.data.remote.model.ClassificationResult.Companion  Float Ncom.example.abonekaptanmobile.data.remote.model.ClassificationResult.Companion  HuggingFaceResponse Ncom.example.abonekaptanmobile.data.remote.model.ClassificationResult.Companion  List Ncom.example.abonekaptanmobile.data.remote.model.ClassificationResult.Companion  String Ncom.example.abonekaptanmobile.data.remote.model.ClassificationResult.Companion  Boolean Lcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult  ClassificationResult Lcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult  Float Lcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult  List Lcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult  String Lcom.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult  Float Gcom.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus  Int Gcom.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus  Long Gcom.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus  String Gcom.example.abonekaptanmobile.data.remote.model.FinalSubscriptionStatus  Int <com.example.abonekaptanmobile.data.remote.model.GmailMessage  List <com.example.abonekaptanmobile.data.remote.model.GmailMessage  MessagePayload <com.example.abonekaptanmobile.data.remote.model.GmailMessage  SerializedName <com.example.abonekaptanmobile.data.remote.model.GmailMessage  String <com.example.abonekaptanmobile.data.remote.model.GmailMessage  Int Hcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse  List Hcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse  	MessageId Hcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse  SerializedName Hcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse  String Hcom.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse  List Ecom.example.abonekaptanmobile.data.remote.model.HuggingFaceParameters  SerializedName Ecom.example.abonekaptanmobile.data.remote.model.HuggingFaceParameters  String Ecom.example.abonekaptanmobile.data.remote.model.HuggingFaceParameters  HuggingFaceParameters Bcom.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest  SerializedName Bcom.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest  String Bcom.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest  Float Ccom.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse  List Ccom.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse  SerializedName Ccom.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse  String Ccom.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse  Boolean Scom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters  Float Scom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters  Int Scom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters  SerializedName Scom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters  #HuggingFaceTextGenerationParameters Pcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest  SerializedName Pcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest  String Pcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest  !HuggingFaceTextGenerationResponse Qcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse  List Qcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse  SerializedName Qcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse  String Qcom.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse  !HuggingFaceTextGenerationResponse [com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse.Companion  List [com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse.Companion  SerializedName [com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse.Companion  String [com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse.Companion  Boolean Fcom.example.abonekaptanmobile.data.remote.model.HybridValidationResult  DetailedClassificationResult Fcom.example.abonekaptanmobile.data.remote.model.HybridValidationResult  Float Fcom.example.abonekaptanmobile.data.remote.model.HybridValidationResult  String Fcom.example.abonekaptanmobile.data.remote.model.HybridValidationResult  Boolean ?com.example.abonekaptanmobile.data.remote.model.LlamaParameters  Float ?com.example.abonekaptanmobile.data.remote.model.LlamaParameters  Int ?com.example.abonekaptanmobile.data.remote.model.LlamaParameters  SerializedName ?com.example.abonekaptanmobile.data.remote.model.LlamaParameters  ReplicateInput <com.example.abonekaptanmobile.data.remote.model.LlamaRequest  SerializedName <com.example.abonekaptanmobile.data.remote.model.LlamaRequest  String <com.example.abonekaptanmobile.data.remote.model.LlamaRequest  List =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  
LlamaResponse =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  SerializedName =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  String =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  getJOINToString =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  getJoinToString =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  joinToString =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  output =com.example.abonekaptanmobile.data.remote.model.LlamaResponse  List Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  
LlamaResponse Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  SerializedName Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  String Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  getJOINToString Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  getJoinToString Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  joinToString Gcom.example.abonekaptanmobile.data.remote.model.LlamaResponse.Companion  SerializedName =com.example.abonekaptanmobile.data.remote.model.MessageHeader  String =com.example.abonekaptanmobile.data.remote.model.MessageHeader  SerializedName 9com.example.abonekaptanmobile.data.remote.model.MessageId  String 9com.example.abonekaptanmobile.data.remote.model.MessageId  Int ?com.example.abonekaptanmobile.data.remote.model.MessagePartBody  SerializedName ?com.example.abonekaptanmobile.data.remote.model.MessagePartBody  String ?com.example.abonekaptanmobile.data.remote.model.MessagePartBody  List >com.example.abonekaptanmobile.data.remote.model.MessagePayload  
MessageHeader >com.example.abonekaptanmobile.data.remote.model.MessagePayload  MessagePartBody >com.example.abonekaptanmobile.data.remote.model.MessagePayload  MessagePayload >com.example.abonekaptanmobile.data.remote.model.MessagePayload  SerializedName >com.example.abonekaptanmobile.data.remote.model.MessagePayload  String >com.example.abonekaptanmobile.data.remote.model.MessagePayload  Boolean >com.example.abonekaptanmobile.data.remote.model.ReplicateInput  Float >com.example.abonekaptanmobile.data.remote.model.ReplicateInput  Int >com.example.abonekaptanmobile.data.remote.model.ReplicateInput  SerializedName >com.example.abonekaptanmobile.data.remote.model.ReplicateInput  String >com.example.abonekaptanmobile.data.remote.model.ReplicateInput  Boolean Fcom.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult  Float Fcom.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult  Int Fcom.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult  String Fcom.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult  Boolean -com.example.abonekaptanmobile.data.repository  CommunityPatternRepository -com.example.abonekaptanmobile.data.repository  EmailRepository -com.example.abonekaptanmobile.data.repository  FeedbackRepository -com.example.abonekaptanmobile.data.repository  Float -com.example.abonekaptanmobile.data.repository  GmailRepository -com.example.abonekaptanmobile.data.repository  HuggingFaceRepository -com.example.abonekaptanmobile.data.repository  Int -com.example.abonekaptanmobile.data.repository  List -com.example.abonekaptanmobile.data.repository  Long -com.example.abonekaptanmobile.data.repository  Pair -com.example.abonekaptanmobile.data.repository  String -com.example.abonekaptanmobile.data.repository  SubscriptionRepository -com.example.abonekaptanmobile.data.repository  listOf -com.example.abonekaptanmobile.data.repository  CommunityPatternDao Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  Inject Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  Int Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  List Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  String Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  SubscriptionPatternEntity Hcom.example.abonekaptanmobile.data.repository.CommunityPatternRepository  EmailDao =com.example.abonekaptanmobile.data.repository.EmailRepository  EmailEntity =com.example.abonekaptanmobile.data.repository.EmailRepository  Flow =com.example.abonekaptanmobile.data.repository.EmailRepository  Inject =com.example.abonekaptanmobile.data.repository.EmailRepository  Int =com.example.abonekaptanmobile.data.repository.EmailRepository  List =com.example.abonekaptanmobile.data.repository.EmailRepository  RawEmail =com.example.abonekaptanmobile.data.repository.EmailRepository  FeedbackDao @com.example.abonekaptanmobile.data.repository.FeedbackRepository  FeedbackEntity @com.example.abonekaptanmobile.data.repository.FeedbackRepository  Inject @com.example.abonekaptanmobile.data.repository.FeedbackRepository  List @com.example.abonekaptanmobile.data.repository.FeedbackRepository  Long @com.example.abonekaptanmobile.data.repository.FeedbackRepository  Boolean =com.example.abonekaptanmobile.data.repository.GmailRepository  GmailApi =com.example.abonekaptanmobile.data.repository.GmailRepository  GmailMessage =com.example.abonekaptanmobile.data.repository.GmailRepository  Inject =com.example.abonekaptanmobile.data.repository.GmailRepository  Int =com.example.abonekaptanmobile.data.repository.GmailRepository  List =com.example.abonekaptanmobile.data.repository.GmailRepository  MessagePayload =com.example.abonekaptanmobile.data.repository.GmailRepository  Pair =com.example.abonekaptanmobile.data.repository.GmailRepository  RawEmail =com.example.abonekaptanmobile.data.repository.GmailRepository  String =com.example.abonekaptanmobile.data.repository.GmailRepository  	getLISTOf =com.example.abonekaptanmobile.data.repository.GmailRepository  	getListOf =com.example.abonekaptanmobile.data.repository.GmailRepository  listOf =com.example.abonekaptanmobile.data.repository.GmailRepository  ClassificationResult Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  DetailedClassificationResult Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  Float Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  HuggingFaceApi Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  HybridValidationResult Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  Inject Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  Int Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  Pair Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  String Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  TwoStageAnalysisResult Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  listOf Ccom.example.abonekaptanmobile.data.repository.HuggingFaceRepository  ClassificationResult Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  DetailedClassificationResult Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  Float Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  HuggingFaceApi Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  HybridValidationResult Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  Inject Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  Int Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  Pair Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  String Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  TwoStageAnalysisResult Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  	getLISTOf Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  	getListOf Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  listOf Mcom.example.abonekaptanmobile.data.repository.HuggingFaceRepository.Companion  Flow Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  Inject Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  Int Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  List Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  SubscriptionDao Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  SubscriptionEntity Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  SubscriptionItem Dcom.example.abonekaptanmobile.data.repository.SubscriptionRepository  	AppModule  com.example.abonekaptanmobile.di  SingletonComponent  com.example.abonekaptanmobile.di  AppDatabase *com.example.abonekaptanmobile.di.AppModule  ApplicationContext *com.example.abonekaptanmobile.di.AppModule  CommunityPatternDao *com.example.abonekaptanmobile.di.AppModule  CommunityPatternRepository *com.example.abonekaptanmobile.di.AppModule  Context *com.example.abonekaptanmobile.di.AppModule  EmailDao *com.example.abonekaptanmobile.di.AppModule  EmailRepository *com.example.abonekaptanmobile.di.AppModule  FeedbackDao *com.example.abonekaptanmobile.di.AppModule  FeedbackRepository *com.example.abonekaptanmobile.di.AppModule  GmailApi *com.example.abonekaptanmobile.di.AppModule  GmailRepository *com.example.abonekaptanmobile.di.AppModule  GoogleAuthManager *com.example.abonekaptanmobile.di.AppModule  HuggingFaceApi *com.example.abonekaptanmobile.di.AppModule  HuggingFaceRepository *com.example.abonekaptanmobile.di.AppModule  OkHttpClient *com.example.abonekaptanmobile.di.AppModule  Provides *com.example.abonekaptanmobile.di.AppModule  	Singleton *com.example.abonekaptanmobile.di.AppModule  SubscriptionClassifier *com.example.abonekaptanmobile.di.AppModule  SubscriptionDao *com.example.abonekaptanmobile.di.AppModule  SubscriptionRepository *com.example.abonekaptanmobile.di.AppModule  Boolean #com.example.abonekaptanmobile.model  CancellationInfo #com.example.abonekaptanmobile.model  ClassifiedEmail #com.example.abonekaptanmobile.model  	EmailType #com.example.abonekaptanmobile.model  Int #com.example.abonekaptanmobile.model  List #com.example.abonekaptanmobile.model  Long #com.example.abonekaptanmobile.model  RawEmail #com.example.abonekaptanmobile.model  Regex #com.example.abonekaptanmobile.model  RegexOption #com.example.abonekaptanmobile.model  String #com.example.abonekaptanmobile.model  SubscriptionItem #com.example.abonekaptanmobile.model  SubscriptionStatus #com.example.abonekaptanmobile.model  SubscriptionType #com.example.abonekaptanmobile.model  TimeUnit #com.example.abonekaptanmobile.model  invoke #com.example.abonekaptanmobile.model  listOf #com.example.abonekaptanmobile.model  setOf #com.example.abonekaptanmobile.model  Long 4com.example.abonekaptanmobile.model.CancellationInfo  String 4com.example.abonekaptanmobile.model.CancellationInfo  Boolean 3com.example.abonekaptanmobile.model.ClassifiedEmail  ClassificationResult 3com.example.abonekaptanmobile.model.ClassifiedEmail  	EmailType 3com.example.abonekaptanmobile.model.ClassifiedEmail  List 3com.example.abonekaptanmobile.model.ClassifiedEmail  Long 3com.example.abonekaptanmobile.model.ClassifiedEmail  RawEmail 3com.example.abonekaptanmobile.model.ClassifiedEmail  String 3com.example.abonekaptanmobile.model.ClassifiedEmail  SubscriptionType 3com.example.abonekaptanmobile.model.ClassifiedEmail  List ,com.example.abonekaptanmobile.model.RawEmail  Long ,com.example.abonekaptanmobile.model.RawEmail  String ,com.example.abonekaptanmobile.model.RawEmail  Int 4com.example.abonekaptanmobile.model.SubscriptionItem  List 4com.example.abonekaptanmobile.model.SubscriptionItem  Long 4com.example.abonekaptanmobile.model.SubscriptionItem  String 4com.example.abonekaptanmobile.model.SubscriptionItem  SubscriptionStatus 4com.example.abonekaptanmobile.model.SubscriptionItem  Float &com.example.abonekaptanmobile.services  List &com.example.abonekaptanmobile.services  RawEmail &com.example.abonekaptanmobile.services  Regex &com.example.abonekaptanmobile.services  RegexOption &com.example.abonekaptanmobile.services  String &com.example.abonekaptanmobile.services  SubscriptionClassifier &com.example.abonekaptanmobile.services  SubscriptionItem &com.example.abonekaptanmobile.services  TimeUnit &com.example.abonekaptanmobile.services  Unit &com.example.abonekaptanmobile.services  capitalizeWords &com.example.abonekaptanmobile.services  invoke &com.example.abonekaptanmobile.services  listOf &com.example.abonekaptanmobile.services  setOf &com.example.abonekaptanmobile.services  CommunityPatternRepository =com.example.abonekaptanmobile.services.SubscriptionClassifier  FinalSubscriptionStatus =com.example.abonekaptanmobile.services.SubscriptionClassifier  Float =com.example.abonekaptanmobile.services.SubscriptionClassifier  HuggingFaceRepository =com.example.abonekaptanmobile.services.SubscriptionClassifier  Inject =com.example.abonekaptanmobile.services.SubscriptionClassifier  List =com.example.abonekaptanmobile.services.SubscriptionClassifier  RawEmail =com.example.abonekaptanmobile.services.SubscriptionClassifier  Regex =com.example.abonekaptanmobile.services.SubscriptionClassifier  RegexOption =com.example.abonekaptanmobile.services.SubscriptionClassifier  String =com.example.abonekaptanmobile.services.SubscriptionClassifier  SubscriptionItem =com.example.abonekaptanmobile.services.SubscriptionClassifier  TimeUnit =com.example.abonekaptanmobile.services.SubscriptionClassifier  Unit =com.example.abonekaptanmobile.services.SubscriptionClassifier  	getLISTOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  	getListOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  getSETOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  getSetOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  inactivityThresholdDays =com.example.abonekaptanmobile.services.SubscriptionClassifier  invoke =com.example.abonekaptanmobile.services.SubscriptionClassifier  listOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  setOf =com.example.abonekaptanmobile.services.SubscriptionClassifier  Boolean (com.example.abonekaptanmobile.ui.screens  
Composable (com.example.abonekaptanmobile.ui.screens  ExperimentalFoundationApi (com.example.abonekaptanmobile.ui.screens  FeedbackDialog (com.example.abonekaptanmobile.ui.screens  
LabTestDialog (com.example.abonekaptanmobile.ui.screens  List (com.example.abonekaptanmobile.ui.screens  OptIn (com.example.abonekaptanmobile.ui.screens  SignInScreen (com.example.abonekaptanmobile.ui.screens  String (com.example.abonekaptanmobile.ui.screens  SubscriptionCard (com.example.abonekaptanmobile.ui.screens  SubscriptionHeader (com.example.abonekaptanmobile.ui.screens  SubscriptionListScreen (com.example.abonekaptanmobile.ui.screens  TestResultCard (com.example.abonekaptanmobile.ui.screens  Unit (com.example.abonekaptanmobile.ui.screens  AboneKaptanMobileTheme &com.example.abonekaptanmobile.ui.theme  BlueInfo &com.example.abonekaptanmobile.ui.theme  Boolean &com.example.abonekaptanmobile.ui.theme  DarkColorScheme &com.example.abonekaptanmobile.ui.theme  GreenConfirm &com.example.abonekaptanmobile.ui.theme  LightColorScheme &com.example.abonekaptanmobile.ui.theme  
OrangeWarning &com.example.abonekaptanmobile.ui.theme  Pink40 &com.example.abonekaptanmobile.ui.theme  Pink80 &com.example.abonekaptanmobile.ui.theme  Purple40 &com.example.abonekaptanmobile.ui.theme  Purple80 &com.example.abonekaptanmobile.ui.theme  PurpleGrey40 &com.example.abonekaptanmobile.ui.theme  PurpleGrey80 &com.example.abonekaptanmobile.ui.theme  RedError &com.example.abonekaptanmobile.ui.theme  
Typography &com.example.abonekaptanmobile.ui.theme  Unit &com.example.abonekaptanmobile.ui.theme  Boolean *com.example.abonekaptanmobile.ui.viewmodel  Float *com.example.abonekaptanmobile.ui.viewmodel  List *com.example.abonekaptanmobile.ui.viewmodel  
MainViewModel *com.example.abonekaptanmobile.ui.viewmodel  MutableStateFlow *com.example.abonekaptanmobile.ui.viewmodel  	StateFlow *com.example.abonekaptanmobile.ui.viewmodel  String *com.example.abonekaptanmobile.ui.viewmodel  asStateFlow *com.example.abonekaptanmobile.ui.viewmodel  	emptyList *com.example.abonekaptanmobile.ui.viewmodel  ActivityResultLauncher 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  ApplicationContext 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Boolean 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Context 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  EmailRepository 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  FeedbackRepository 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Float 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  GmailRepository 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  GoogleAuthManager 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  GoogleSignInAccount 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  HybridApproachTester 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Inject 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Intent 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  List 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  MutableStateFlow 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  	StateFlow 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  String 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  SubscriptionClassifier 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  SubscriptionItem 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  SubscriptionRepository 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  SubscriptionStatus 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Task 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _emailProcessingProgress 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _emailProcessingStatus 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _error 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  
_isLoading 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _isSignedIn 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _isSigningIn 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  _subscriptions 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  asStateFlow 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  	emptyList 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  getASStateFlow 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  getAsStateFlow 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  getEMPTYList 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  getEmptyList 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  googleAuthManager 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  hybridApproachTester 8com.example.abonekaptanmobile.ui.viewmodel.MainViewModel  Boolean #com.example.abonekaptanmobile.utils  Float #com.example.abonekaptanmobile.utils  HybridApproachTester #com.example.abonekaptanmobile.utils  List #com.example.abonekaptanmobile.utils  MutableStateFlow #com.example.abonekaptanmobile.utils  String #com.example.abonekaptanmobile.utils  
TestResult #com.example.abonekaptanmobile.utils  asStateFlow #com.example.abonekaptanmobile.utils  com #com.example.abonekaptanmobile.utils  	emptyList #com.example.abonekaptanmobile.utils  setOf #com.example.abonekaptanmobile.utils  Boolean 8com.example.abonekaptanmobile.utils.HybridApproachTester  HuggingFaceRepository 8com.example.abonekaptanmobile.utils.HybridApproachTester  Inject 8com.example.abonekaptanmobile.utils.HybridApproachTester  List 8com.example.abonekaptanmobile.utils.HybridApproachTester  MutableStateFlow 8com.example.abonekaptanmobile.utils.HybridApproachTester  RawEmail 8com.example.abonekaptanmobile.utils.HybridApproachTester  	StateFlow 8com.example.abonekaptanmobile.utils.HybridApproachTester  String 8com.example.abonekaptanmobile.utils.HybridApproachTester  
TestResult 8com.example.abonekaptanmobile.utils.HybridApproachTester  _isTestingInProgress 8com.example.abonekaptanmobile.utils.HybridApproachTester  _testResults 8com.example.abonekaptanmobile.utils.HybridApproachTester  asStateFlow 8com.example.abonekaptanmobile.utils.HybridApproachTester  com 8com.example.abonekaptanmobile.utils.HybridApproachTester  	emptyList 8com.example.abonekaptanmobile.utils.HybridApproachTester  getASStateFlow 8com.example.abonekaptanmobile.utils.HybridApproachTester  getAsStateFlow 8com.example.abonekaptanmobile.utils.HybridApproachTester  getEMPTYList 8com.example.abonekaptanmobile.utils.HybridApproachTester  getEmptyList 8com.example.abonekaptanmobile.utils.HybridApproachTester  getSETOf 8com.example.abonekaptanmobile.utils.HybridApproachTester  getSetOf 8com.example.abonekaptanmobile.utils.HybridApproachTester  isTestingInProgress 8com.example.abonekaptanmobile.utils.HybridApproachTester  setOf 8com.example.abonekaptanmobile.utils.HybridApproachTester  testResults 8com.example.abonekaptanmobile.utils.HybridApproachTester  Boolean Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  HuggingFaceRepository Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  Inject Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  List Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  MutableStateFlow Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  RawEmail Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  	StateFlow Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  String Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  
TestResult Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  asStateFlow Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  com Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  	emptyList Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getASStateFlow Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getAsStateFlow Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getEMPTYList Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getEmptyList Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getSETOf Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  getSetOf Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  setOf Bcom.example.abonekaptanmobile.utils.HybridApproachTester.Companion  Boolean .com.example.abonekaptanmobile.utils.TestResult  Float .com.example.abonekaptanmobile.utils.TestResult  String .com.example.abonekaptanmobile.utils.TestResult  ProcessFeedbackWorker %com.example.abonekaptanmobile.workers  AppDatabase ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  Assisted ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  AssistedInject ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  CommunityPatternRepository ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  Context ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  FeedbackRepository ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  Result ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  WorkerParameters ;com.example.abonekaptanmobile.workers.ProcessFeedbackWorker  AppDatabase Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  Assisted Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  AssistedInject Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  CommunityPatternRepository Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  Context Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  FeedbackRepository Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  Result Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  WorkerParameters Ecom.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInAccount &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  	getClient 3com.google.android.gms.auth.api.signin.GoogleSignIn  equals :com.google.android.gms.auth.api.signin.GoogleSignInAccount  Builder :com.google.android.gms.auth.api.signin.GoogleSignInOptions  DEFAULT_SIGN_IN :com.google.android.gms.auth.api.signin.GoogleSignInOptions  build Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestEmail Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  
requestScopes Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  ApiException !com.google.android.gms.common.api  Scope !com.google.android.gms.common.api  Task com.google.android.gms.tasks  GoogleAccountCredential <com.google.api.client.googleapis.extensions.android.gms.auth  GmailScopes com.google.api.services.gmail  GMAIL_READONLY )com.google.api.services.gmail.GmailScopes  SerializedName com.google.gson.annotations  Module dagger  Provides dagger  Assisted dagger.assisted  AssistedInject dagger.assisted  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  IOException java.io  
Configuration 	java.lang  EmailEntity 	java.lang  	Exception 	java.lang  ExperimentalFoundationApi 	java.lang  ExperimentalMaterial3Api 	java.lang  FeedbackEntity 	java.lang  GmailScopes 	java.lang  GoogleSignIn 	java.lang  GoogleSignInOptions 	java.lang  Log 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  PatternType 	java.lang  Regex 	java.lang  RegexOption 	java.lang  Scope 	java.lang  SingletonComponent 	java.lang  SubscriptionEntity 	java.lang  SubscriptionPatternEntity 	java.lang  TimeUnit 	java.lang  Void 	java.lang  asStateFlow 	java.lang  com 	java.lang  	emptyList 	java.lang  invoke 	java.lang  joinToString 	java.lang  listOf 	java.lang  setOf 	java.lang  
trimIndent 	java.lang  message java.lang.Exception  StandardCharsets java.nio.charset  SimpleDateFormat 	java.text  Collections 	java.util  
Composable 	java.util  ExperimentalFoundationApi 	java.util  TimeUnit java.util.concurrent  DAYS java.util.concurrent.TimeUnit  toMillis java.util.concurrent.TimeUnit  Pattern java.util.regex  PatternSyntaxException java.util.regex  Inject javax.inject  	Singleton javax.inject  Any kotlin  Array kotlin  Boolean kotlin  
Configuration kotlin  Double kotlin  EmailEntity kotlin  	Exception kotlin  ExperimentalFoundationApi kotlin  ExperimentalMaterial3Api kotlin  FeedbackEntity kotlin  Float kotlin  GmailScopes kotlin  GoogleSignIn kotlin  GoogleSignInOptions kotlin  Int kotlin  Log kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  Pair kotlin  PatternType kotlin  Regex kotlin  RegexOption kotlin  Scope kotlin  SingletonComponent kotlin  String kotlin  SubscriptionEntity kotlin  SubscriptionPatternEntity kotlin  TimeUnit kotlin  Unit kotlin  Void kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  	emptyList kotlin  invoke kotlin  joinToString kotlin  listOf kotlin  setOf kotlin  
trimIndent kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  
getTRIMIndent 
kotlin.String  
getTrimIndent 
kotlin.String  
Configuration kotlin.annotation  EmailEntity kotlin.annotation  	Exception kotlin.annotation  ExperimentalFoundationApi kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  FeedbackEntity kotlin.annotation  GmailScopes kotlin.annotation  GoogleSignIn kotlin.annotation  GoogleSignInOptions kotlin.annotation  Log kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  Pair kotlin.annotation  PatternType kotlin.annotation  Regex kotlin.annotation  RegexOption kotlin.annotation  Scope kotlin.annotation  SingletonComponent kotlin.annotation  SubscriptionEntity kotlin.annotation  SubscriptionPatternEntity kotlin.annotation  TimeUnit kotlin.annotation  Void kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  invoke kotlin.annotation  joinToString kotlin.annotation  listOf kotlin.annotation  setOf kotlin.annotation  
trimIndent kotlin.annotation  
Configuration kotlin.collections  EmailEntity kotlin.collections  	Exception kotlin.collections  ExperimentalFoundationApi kotlin.collections  ExperimentalMaterial3Api kotlin.collections  FeedbackEntity kotlin.collections  GmailScopes kotlin.collections  GoogleSignIn kotlin.collections  GoogleSignInOptions kotlin.collections  List kotlin.collections  Log kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  Pair kotlin.collections  PatternType kotlin.collections  Regex kotlin.collections  RegexOption kotlin.collections  Scope kotlin.collections  Set kotlin.collections  SingletonComponent kotlin.collections  SubscriptionEntity kotlin.collections  SubscriptionPatternEntity kotlin.collections  TimeUnit kotlin.collections  Void kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  invoke kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  setOf kotlin.collections  
trimIndent kotlin.collections  getJOINToString kotlin.collections.List  getJoinToString kotlin.collections.List  
Configuration kotlin.comparisons  EmailEntity kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalFoundationApi kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  FeedbackEntity kotlin.comparisons  GmailScopes kotlin.comparisons  GoogleSignIn kotlin.comparisons  GoogleSignInOptions kotlin.comparisons  Log kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Pair kotlin.comparisons  PatternType kotlin.comparisons  Regex kotlin.comparisons  RegexOption kotlin.comparisons  Scope kotlin.comparisons  SingletonComponent kotlin.comparisons  SubscriptionEntity kotlin.comparisons  SubscriptionPatternEntity kotlin.comparisons  TimeUnit kotlin.comparisons  Void kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  invoke kotlin.comparisons  joinToString kotlin.comparisons  listOf kotlin.comparisons  setOf kotlin.comparisons  
trimIndent kotlin.comparisons  
Configuration 	kotlin.io  EmailEntity 	kotlin.io  	Exception 	kotlin.io  ExperimentalFoundationApi 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  FeedbackEntity 	kotlin.io  GmailScopes 	kotlin.io  GoogleSignIn 	kotlin.io  GoogleSignInOptions 	kotlin.io  Log 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  Pair 	kotlin.io  PatternType 	kotlin.io  Regex 	kotlin.io  RegexOption 	kotlin.io  Scope 	kotlin.io  SingletonComponent 	kotlin.io  SubscriptionEntity 	kotlin.io  SubscriptionPatternEntity 	kotlin.io  TimeUnit 	kotlin.io  Void 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  invoke 	kotlin.io  joinToString 	kotlin.io  listOf 	kotlin.io  setOf 	kotlin.io  
trimIndent 	kotlin.io  
Configuration 
kotlin.jvm  EmailEntity 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalFoundationApi 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  FeedbackEntity 
kotlin.jvm  GmailScopes 
kotlin.jvm  GoogleSignIn 
kotlin.jvm  GoogleSignInOptions 
kotlin.jvm  Log 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Pair 
kotlin.jvm  PatternType 
kotlin.jvm  Regex 
kotlin.jvm  RegexOption 
kotlin.jvm  Scope 
kotlin.jvm  SingletonComponent 
kotlin.jvm  SubscriptionEntity 
kotlin.jvm  SubscriptionPatternEntity 
kotlin.jvm  TimeUnit 
kotlin.jvm  Void 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  invoke 
kotlin.jvm  joinToString 
kotlin.jvm  listOf 
kotlin.jvm  setOf 
kotlin.jvm  
trimIndent 
kotlin.jvm  
Configuration 
kotlin.ranges  EmailEntity 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalFoundationApi 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  FeedbackEntity 
kotlin.ranges  GmailScopes 
kotlin.ranges  GoogleSignIn 
kotlin.ranges  GoogleSignInOptions 
kotlin.ranges  Log 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Pair 
kotlin.ranges  PatternType 
kotlin.ranges  Regex 
kotlin.ranges  RegexOption 
kotlin.ranges  Scope 
kotlin.ranges  SingletonComponent 
kotlin.ranges  SubscriptionEntity 
kotlin.ranges  SubscriptionPatternEntity 
kotlin.ranges  TimeUnit 
kotlin.ranges  Void 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  invoke 
kotlin.ranges  joinToString 
kotlin.ranges  listOf 
kotlin.ranges  setOf 
kotlin.ranges  
trimIndent 
kotlin.ranges  KClass kotlin.reflect  
Configuration kotlin.sequences  EmailEntity kotlin.sequences  	Exception kotlin.sequences  ExperimentalFoundationApi kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  FeedbackEntity kotlin.sequences  GmailScopes kotlin.sequences  GoogleSignIn kotlin.sequences  GoogleSignInOptions kotlin.sequences  Log kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  Pair kotlin.sequences  PatternType kotlin.sequences  Regex kotlin.sequences  RegexOption kotlin.sequences  Scope kotlin.sequences  SingletonComponent kotlin.sequences  SubscriptionEntity kotlin.sequences  SubscriptionPatternEntity kotlin.sequences  TimeUnit kotlin.sequences  Void kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  invoke kotlin.sequences  joinToString kotlin.sequences  listOf kotlin.sequences  setOf kotlin.sequences  
trimIndent kotlin.sequences  
Configuration kotlin.text  EmailEntity kotlin.text  	Exception kotlin.text  ExperimentalFoundationApi kotlin.text  ExperimentalMaterial3Api kotlin.text  FeedbackEntity kotlin.text  GmailScopes kotlin.text  GoogleSignIn kotlin.text  GoogleSignInOptions kotlin.text  Log kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  Pair kotlin.text  PatternType kotlin.text  Regex kotlin.text  RegexOption kotlin.text  Scope kotlin.text  SingletonComponent kotlin.text  SubscriptionEntity kotlin.text  SubscriptionPatternEntity kotlin.text  TimeUnit kotlin.text  Void kotlin.text  asStateFlow kotlin.text  com kotlin.text  	emptyList kotlin.text  invoke kotlin.text  joinToString kotlin.text  listOf kotlin.text  setOf kotlin.text  
trimIndent kotlin.text  invoke kotlin.text.Regex.Companion  IGNORE_CASE kotlin.text.RegexOption  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  async kotlinx.coroutines  awaitAll kotlinx.coroutines  coroutineScope kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  OkHttpClient okhttp3  Request okhttp3  HttpLoggingInterceptor okhttp3.logging  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  Body retrofit2.http  GET retrofit2.http  Header retrofit2.http  POST retrofit2.http  Path retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            