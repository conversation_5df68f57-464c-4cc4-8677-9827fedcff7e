package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.EmailEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class EmailDao_Impl implements EmailDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<EmailEntity> __insertionAdapterOfEmailEntity;

  private final EntityDeletionOrUpdateAdapter<EmailEntity> __deletionAdapterOfEmailEntity;

  private final EntityDeletionOrUpdateAdapter<EmailEntity> __updateAdapterOfEmailEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteEmailById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllEmails;

  public EmailDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfEmailEntity = new EntityInsertionAdapter<EmailEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `emails` (`id`,`from`,`subject`,`snippet`,`bodyPlainText`,`bodySnippet`,`date`,`threadId`,`labelIds`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmailEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getFrom() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFrom());
        }
        if (entity.getSubject() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSubject());
        }
        if (entity.getSnippet() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSnippet());
        }
        if (entity.getBodyPlainText() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBodyPlainText());
        }
        if (entity.getBodySnippet() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getBodySnippet());
        }
        statement.bindLong(7, entity.getDate());
        if (entity.getThreadId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getThreadId());
        }
        if (entity.getLabelIds() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getLabelIds());
        }
      }
    };
    this.__deletionAdapterOfEmailEntity = new EntityDeletionOrUpdateAdapter<EmailEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `emails` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmailEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfEmailEntity = new EntityDeletionOrUpdateAdapter<EmailEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `emails` SET `id` = ?,`from` = ?,`subject` = ?,`snippet` = ?,`bodyPlainText` = ?,`bodySnippet` = ?,`date` = ?,`threadId` = ?,`labelIds` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final EmailEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getFrom() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFrom());
        }
        if (entity.getSubject() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSubject());
        }
        if (entity.getSnippet() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSnippet());
        }
        if (entity.getBodyPlainText() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getBodyPlainText());
        }
        if (entity.getBodySnippet() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getBodySnippet());
        }
        statement.bindLong(7, entity.getDate());
        if (entity.getThreadId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getThreadId());
        }
        if (entity.getLabelIds() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getLabelIds());
        }
        if (entity.getId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteEmailById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM emails WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllEmails = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM emails";
        return _query;
      }
    };
  }

  @Override
  public Object insertEmails(final List<EmailEntity> emails,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmailEntity.insert(emails);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertEmail(final EmailEntity email, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfEmailEntity.insert(email);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmail(final EmailEntity email, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfEmailEntity.handle(email);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateEmail(final EmailEntity email, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfEmailEntity.handle(email);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteEmailById(final String emailId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteEmailById.acquire();
        int _argIndex = 1;
        if (emailId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, emailId);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteEmailById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllEmails(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllEmails.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllEmails.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<EmailEntity>> getAllEmails() {
    final String _sql = "SELECT * FROM emails ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emails"}, new Callable<List<EmailEntity>>() {
      @Override
      @NonNull
      public List<EmailEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "snippet");
          final int _cursorIndexOfBodyPlainText = CursorUtil.getColumnIndexOrThrow(_cursor, "bodyPlainText");
          final int _cursorIndexOfBodySnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "bodySnippet");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfThreadId = CursorUtil.getColumnIndexOrThrow(_cursor, "threadId");
          final int _cursorIndexOfLabelIds = CursorUtil.getColumnIndexOrThrow(_cursor, "labelIds");
          final List<EmailEntity> _result = new ArrayList<EmailEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmailEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpSnippet;
            if (_cursor.isNull(_cursorIndexOfSnippet)) {
              _tmpSnippet = null;
            } else {
              _tmpSnippet = _cursor.getString(_cursorIndexOfSnippet);
            }
            final String _tmpBodyPlainText;
            if (_cursor.isNull(_cursorIndexOfBodyPlainText)) {
              _tmpBodyPlainText = null;
            } else {
              _tmpBodyPlainText = _cursor.getString(_cursorIndexOfBodyPlainText);
            }
            final String _tmpBodySnippet;
            if (_cursor.isNull(_cursorIndexOfBodySnippet)) {
              _tmpBodySnippet = null;
            } else {
              _tmpBodySnippet = _cursor.getString(_cursorIndexOfBodySnippet);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpThreadId;
            if (_cursor.isNull(_cursorIndexOfThreadId)) {
              _tmpThreadId = null;
            } else {
              _tmpThreadId = _cursor.getString(_cursorIndexOfThreadId);
            }
            final String _tmpLabelIds;
            if (_cursor.isNull(_cursorIndexOfLabelIds)) {
              _tmpLabelIds = null;
            } else {
              _tmpLabelIds = _cursor.getString(_cursorIndexOfLabelIds);
            }
            _item = new EmailEntity(_tmpId,_tmpFrom,_tmpSubject,_tmpSnippet,_tmpBodyPlainText,_tmpBodySnippet,_tmpDate,_tmpThreadId,_tmpLabelIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmailById(final String emailId,
      final Continuation<? super EmailEntity> $completion) {
    final String _sql = "SELECT * FROM emails WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (emailId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, emailId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<EmailEntity>() {
      @Override
      @Nullable
      public EmailEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "snippet");
          final int _cursorIndexOfBodyPlainText = CursorUtil.getColumnIndexOrThrow(_cursor, "bodyPlainText");
          final int _cursorIndexOfBodySnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "bodySnippet");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfThreadId = CursorUtil.getColumnIndexOrThrow(_cursor, "threadId");
          final int _cursorIndexOfLabelIds = CursorUtil.getColumnIndexOrThrow(_cursor, "labelIds");
          final EmailEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpSnippet;
            if (_cursor.isNull(_cursorIndexOfSnippet)) {
              _tmpSnippet = null;
            } else {
              _tmpSnippet = _cursor.getString(_cursorIndexOfSnippet);
            }
            final String _tmpBodyPlainText;
            if (_cursor.isNull(_cursorIndexOfBodyPlainText)) {
              _tmpBodyPlainText = null;
            } else {
              _tmpBodyPlainText = _cursor.getString(_cursorIndexOfBodyPlainText);
            }
            final String _tmpBodySnippet;
            if (_cursor.isNull(_cursorIndexOfBodySnippet)) {
              _tmpBodySnippet = null;
            } else {
              _tmpBodySnippet = _cursor.getString(_cursorIndexOfBodySnippet);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpThreadId;
            if (_cursor.isNull(_cursorIndexOfThreadId)) {
              _tmpThreadId = null;
            } else {
              _tmpThreadId = _cursor.getString(_cursorIndexOfThreadId);
            }
            final String _tmpLabelIds;
            if (_cursor.isNull(_cursorIndexOfLabelIds)) {
              _tmpLabelIds = null;
            } else {
              _tmpLabelIds = _cursor.getString(_cursorIndexOfLabelIds);
            }
            _result = new EmailEntity(_tmpId,_tmpFrom,_tmpSubject,_tmpSnippet,_tmpBodyPlainText,_tmpBodySnippet,_tmpDate,_tmpThreadId,_tmpLabelIds);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<EmailEntity>> getEmailsBySender(final String senderPattern) {
    final String _sql = "SELECT * FROM emails WHERE `from` LIKE ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (senderPattern == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, senderPattern);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emails"}, new Callable<List<EmailEntity>>() {
      @Override
      @NonNull
      public List<EmailEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "snippet");
          final int _cursorIndexOfBodyPlainText = CursorUtil.getColumnIndexOrThrow(_cursor, "bodyPlainText");
          final int _cursorIndexOfBodySnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "bodySnippet");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfThreadId = CursorUtil.getColumnIndexOrThrow(_cursor, "threadId");
          final int _cursorIndexOfLabelIds = CursorUtil.getColumnIndexOrThrow(_cursor, "labelIds");
          final List<EmailEntity> _result = new ArrayList<EmailEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmailEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpSnippet;
            if (_cursor.isNull(_cursorIndexOfSnippet)) {
              _tmpSnippet = null;
            } else {
              _tmpSnippet = _cursor.getString(_cursorIndexOfSnippet);
            }
            final String _tmpBodyPlainText;
            if (_cursor.isNull(_cursorIndexOfBodyPlainText)) {
              _tmpBodyPlainText = null;
            } else {
              _tmpBodyPlainText = _cursor.getString(_cursorIndexOfBodyPlainText);
            }
            final String _tmpBodySnippet;
            if (_cursor.isNull(_cursorIndexOfBodySnippet)) {
              _tmpBodySnippet = null;
            } else {
              _tmpBodySnippet = _cursor.getString(_cursorIndexOfBodySnippet);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpThreadId;
            if (_cursor.isNull(_cursorIndexOfThreadId)) {
              _tmpThreadId = null;
            } else {
              _tmpThreadId = _cursor.getString(_cursorIndexOfThreadId);
            }
            final String _tmpLabelIds;
            if (_cursor.isNull(_cursorIndexOfLabelIds)) {
              _tmpLabelIds = null;
            } else {
              _tmpLabelIds = _cursor.getString(_cursorIndexOfLabelIds);
            }
            _item = new EmailEntity(_tmpId,_tmpFrom,_tmpSubject,_tmpSnippet,_tmpBodyPlainText,_tmpBodySnippet,_tmpDate,_tmpThreadId,_tmpLabelIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<EmailEntity>> getEmailsBySubject(final String subjectPattern) {
    final String _sql = "SELECT * FROM emails WHERE subject LIKE ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (subjectPattern == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, subjectPattern);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emails"}, new Callable<List<EmailEntity>>() {
      @Override
      @NonNull
      public List<EmailEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "snippet");
          final int _cursorIndexOfBodyPlainText = CursorUtil.getColumnIndexOrThrow(_cursor, "bodyPlainText");
          final int _cursorIndexOfBodySnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "bodySnippet");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfThreadId = CursorUtil.getColumnIndexOrThrow(_cursor, "threadId");
          final int _cursorIndexOfLabelIds = CursorUtil.getColumnIndexOrThrow(_cursor, "labelIds");
          final List<EmailEntity> _result = new ArrayList<EmailEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmailEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpSnippet;
            if (_cursor.isNull(_cursorIndexOfSnippet)) {
              _tmpSnippet = null;
            } else {
              _tmpSnippet = _cursor.getString(_cursorIndexOfSnippet);
            }
            final String _tmpBodyPlainText;
            if (_cursor.isNull(_cursorIndexOfBodyPlainText)) {
              _tmpBodyPlainText = null;
            } else {
              _tmpBodyPlainText = _cursor.getString(_cursorIndexOfBodyPlainText);
            }
            final String _tmpBodySnippet;
            if (_cursor.isNull(_cursorIndexOfBodySnippet)) {
              _tmpBodySnippet = null;
            } else {
              _tmpBodySnippet = _cursor.getString(_cursorIndexOfBodySnippet);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpThreadId;
            if (_cursor.isNull(_cursorIndexOfThreadId)) {
              _tmpThreadId = null;
            } else {
              _tmpThreadId = _cursor.getString(_cursorIndexOfThreadId);
            }
            final String _tmpLabelIds;
            if (_cursor.isNull(_cursorIndexOfLabelIds)) {
              _tmpLabelIds = null;
            } else {
              _tmpLabelIds = _cursor.getString(_cursorIndexOfLabelIds);
            }
            _item = new EmailEntity(_tmpId,_tmpFrom,_tmpSubject,_tmpSnippet,_tmpBodyPlainText,_tmpBodySnippet,_tmpDate,_tmpThreadId,_tmpLabelIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<EmailEntity>> getEmailsByDateRange(final long startDate, final long endDate) {
    final String _sql = "SELECT * FROM emails WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"emails"}, new Callable<List<EmailEntity>>() {
      @Override
      @NonNull
      public List<EmailEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "snippet");
          final int _cursorIndexOfBodyPlainText = CursorUtil.getColumnIndexOrThrow(_cursor, "bodyPlainText");
          final int _cursorIndexOfBodySnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "bodySnippet");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfThreadId = CursorUtil.getColumnIndexOrThrow(_cursor, "threadId");
          final int _cursorIndexOfLabelIds = CursorUtil.getColumnIndexOrThrow(_cursor, "labelIds");
          final List<EmailEntity> _result = new ArrayList<EmailEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final EmailEntity _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpSnippet;
            if (_cursor.isNull(_cursorIndexOfSnippet)) {
              _tmpSnippet = null;
            } else {
              _tmpSnippet = _cursor.getString(_cursorIndexOfSnippet);
            }
            final String _tmpBodyPlainText;
            if (_cursor.isNull(_cursorIndexOfBodyPlainText)) {
              _tmpBodyPlainText = null;
            } else {
              _tmpBodyPlainText = _cursor.getString(_cursorIndexOfBodyPlainText);
            }
            final String _tmpBodySnippet;
            if (_cursor.isNull(_cursorIndexOfBodySnippet)) {
              _tmpBodySnippet = null;
            } else {
              _tmpBodySnippet = _cursor.getString(_cursorIndexOfBodySnippet);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final String _tmpThreadId;
            if (_cursor.isNull(_cursorIndexOfThreadId)) {
              _tmpThreadId = null;
            } else {
              _tmpThreadId = _cursor.getString(_cursorIndexOfThreadId);
            }
            final String _tmpLabelIds;
            if (_cursor.isNull(_cursorIndexOfLabelIds)) {
              _tmpLabelIds = null;
            } else {
              _tmpLabelIds = _cursor.getString(_cursorIndexOfLabelIds);
            }
            _item = new EmailEntity(_tmpId,_tmpFrom,_tmpSubject,_tmpSnippet,_tmpBodyPlainText,_tmpBodySnippet,_tmpDate,_tmpThreadId,_tmpLabelIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getEmailCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM emails";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
